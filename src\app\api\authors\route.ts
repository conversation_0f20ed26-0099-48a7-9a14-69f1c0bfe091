import { NextRequest, NextResponse } from 'next/server';
import { authorService } from '@/lib/supabase/services';

// 获取所有作者
export async function GET() {
  try {
    const authors = await authorService.getAll();
    
    return NextResponse.json({
      success: true,
      data: authors
    });
    
  } catch (error) {
    console.error('Authors fetch error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch authors',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 创建新作者
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // 验证请求数据
    if (!body.name) {
      return NextResponse.json(
        { error: 'Missing required field: name' },
        { status: 400 }
      );
    }
    
    const author = await authorService.create({
      name: body.name,
      bio: body.bio || null,
      avatar_url: body.avatar_url || null,
      email: body.email || null,
      website: body.website || null,
      social_links: body.social_links || {}
    });
    
    return NextResponse.json({
      success: true,
      data: author
    });
    
  } catch (error) {
    console.error('Author creation error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create author',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
