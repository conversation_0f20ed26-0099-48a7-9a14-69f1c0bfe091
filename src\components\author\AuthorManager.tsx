'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { User, Mail, Globe, Edit, Trash2 } from 'lucide-react';
import type { Author } from '@/types/blog';

interface AuthorManagerProps {
  onAuthorSelected?: (author: Author) => void;
}

export default function AuthorManager({ onAuthorSelected }: AuthorManagerProps) {
  const [authors, setAuthors] = useState<Author[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAuthor, setEditingAuthor] = useState<Author | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    bio: '',
    email: '',
    website: '',
    avatar_url: '',
    social_links: {
      twitter: '',
      linkedin: '',
      github: ''
    }
  });

  // 加载作者列表
  const loadAuthors = async () => {
    try {
      const response = await fetch('/api/authors');
      const result = await response.json();
      
      if (result.success) {
        setAuthors(result.data);
      }
    } catch (error) {
      console.error('Failed to load authors:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadAuthors();
  }, []);

  // 重置表单
  const resetForm = () => {
    setFormData({
      name: '',
      bio: '',
      email: '',
      website: '',
      avatar_url: '',
      social_links: {
        twitter: '',
        linkedin: '',
        github: ''
      }
    });
    setEditingAuthor(null);
  };

  // 编辑作者
  const handleEdit = (author: Author) => {
    setEditingAuthor(author);
    setFormData({
      name: author.name,
      bio: author.bio || '',
      email: author.email || '',
      website: author.website || '',
      avatar_url: author.avatar_url || '',
      social_links: {
        twitter: author.social_links?.twitter || '',
        linkedin: author.social_links?.linkedin || '',
        github: author.social_links?.github || ''
      }
    });
    setIsDialogOpen(true);
  };

  // 创建或更新作者
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name) {
      alert('请输入作者姓名');
      return;
    }

    setIsCreating(true);
    
    try {
      const url = editingAuthor ? `/api/authors/${editingAuthor.id}` : '/api/authors';
      const method = editingAuthor ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Operation failed');
      }

      // 重新加载作者列表
      await loadAuthors();
      
      // 重置表单
      resetForm();
      setIsDialogOpen(false);
      
      alert(editingAuthor ? '作者更新成功！' : '作者创建成功！');
      
    } catch (error) {
      console.error('Author operation error:', error);
      alert(`操作失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsCreating(false);
    }
  };

  // 删除作者
  const handleDelete = async (author: Author) => {
    if (!confirm(`确定要删除作者"${author.name}"吗？这将同时删除该作者的所有文章。`)) {
      return;
    }

    try {
      const response = await fetch(`/api/authors/${author.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Delete failed');
      }

      // 重新加载作者列表
      await loadAuthors();
      alert('作者删除成功！');
      
    } catch (error) {
      console.error('Author deletion error:', error);
      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">加载中...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">作者管理</h2>
        
        <Dialog open={isDialogOpen} onOpenChange={(open) => {
          setIsDialogOpen(open);
          if (!open) resetForm();
        }}>
          <DialogTrigger asChild>
            <Button>添加作者</Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingAuthor ? '编辑作者' : '添加新作者'}
              </DialogTitle>
              <DialogDescription>
                {editingAuthor ? '修改作者信息' : '创建一个新的作者档案'}
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">姓名 *</label>
                  <Input
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="作者姓名"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">邮箱</label>
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">个人简介</label>
                <Textarea
                  value={formData.bio}
                  onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                  placeholder="简单介绍一下作者"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">网站</label>
                  <Input
                    value={formData.website}
                    onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                    placeholder="https://example.com"
                  />
                </div>
                
                <div className="space-y-2">
                  <label className="text-sm font-medium">头像URL</label>
                  <Input
                    value={formData.avatar_url}
                    onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}
                    placeholder="https://example.com/avatar.jpg"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">社交媒体</label>
                <div className="grid grid-cols-3 gap-2">
                  <Input
                    value={formData.social_links.twitter}
                    onChange={(e) => setFormData({
                      ...formData,
                      social_links: { ...formData.social_links, twitter: e.target.value }
                    })}
                    placeholder="Twitter用户名"
                  />
                  <Input
                    value={formData.social_links.linkedin}
                    onChange={(e) => setFormData({
                      ...formData,
                      social_links: { ...formData.social_links, linkedin: e.target.value }
                    })}
                    placeholder="LinkedIn用户名"
                  />
                  <Input
                    value={formData.social_links.github}
                    onChange={(e) => setFormData({
                      ...formData,
                      social_links: { ...formData.social_links, github: e.target.value }
                    })}
                    placeholder="GitHub用户名"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  取消
                </Button>
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? '保存中...' : editingAuthor ? '更新' : '创建'}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* 作者列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {authors.map((author) => (
          <Card 
            key={author.id} 
            className="cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => onAuthorSelected?.(author)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarImage src={author.avatar_url || undefined} />
                  <AvatarFallback>
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <CardTitle className="text-lg">{author.name}</CardTitle>
                  {author.email && (
                    <div className="flex items-center text-sm text-gray-500">
                      <Mail className="w-3 h-3 mr-1" />
                      {author.email}
                    </div>
                  )}
                </div>
                <div className="flex space-x-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEdit(author);
                    }}
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(author);
                    }}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {author.bio && (
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {author.bio}
                </p>
              )}
              
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>
                  创建于 {new Date(author.created_at).toLocaleDateString('zh-CN')}
                </span>
                {author.website && (
                  <div className="flex items-center">
                    <Globe className="w-3 h-3 mr-1" />
                    <span>有网站</span>
                  </div>
                )}
              </div>
              
              {(author.social_links?.twitter || author.social_links?.linkedin || author.social_links?.github) && (
                <div className="flex space-x-1 mt-2">
                  {author.social_links?.twitter && <Badge variant="outline" className="text-xs">Twitter</Badge>}
                  {author.social_links?.linkedin && <Badge variant="outline" className="text-xs">LinkedIn</Badge>}
                  {author.social_links?.github && <Badge variant="outline" className="text-xs">GitHub</Badge>}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {authors.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <p className="text-gray-500 mb-4">还没有添加任何作者</p>
            <Button onClick={() => setIsDialogOpen(true)}>
              添加第一个作者
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
