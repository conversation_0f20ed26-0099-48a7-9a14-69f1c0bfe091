import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// 检查环境变量是否配置
const isConfigured = supabaseUrl &&
  supabaseAnonKey &&
  supabaseUrl !== 'your_supabase_url_here' &&
  supabaseAnonKey !== 'your_supabase_anon_key_here';

// 如果未配置，使用模拟客户端
export const supabase = isConfigured
  ? createClient<Database>(supabaseUrl!, supabaseAnonKey!)
  : null;

// 服务端客户端（用于服务端操作）
export const createServerClient = () => {
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!isConfigured || !supabaseServiceKey || supabaseServiceKey === 'your_supabase_service_role_key_here') {
    return null;
  }

  return createClient<Database>(supabaseUrl!, supabaseServiceKey);
};
