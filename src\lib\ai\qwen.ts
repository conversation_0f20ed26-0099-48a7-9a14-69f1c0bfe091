import OpenAI from 'openai';

if (!process.env.QWEN_API_KEY) {
  throw new Error('Missing Qwen API key');
}

// 使用 OpenAI SDK 连接 Qwen API（兼容 OpenAI 格式）
export const qwen = new OpenAI({
  apiKey: process.env.QWEN_API_KEY,
  baseURL: process.env.QWEN_BASE_URL || 'https://dashscope.aliyuncs.com/compatible-mode/v1',
});

// 默认的AI配置
export const AI_CONFIG = {
  model: 'qwen-plus-latest', // 使用 Qwen Plus 最新版本
  temperature: 0.7,
  max_tokens: 4000,
  top_p: 1,
  frequency_penalty: 0,
  presence_penalty: 0,
};

// 生成博文的函数
export async function generateBlogPost(prompt: string, config = AI_CONFIG) {
  try {
    const response = await qwen.chat.completions.create({
      model: config.model,
      messages: [
        {
          role: 'system',
          content: `你是一个专业的博客写手，擅长创作高质量、SEO友好的博文内容。请严格按照用户要求的格式返回结果。`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: config.temperature,
      max_tokens: config.max_tokens,
      top_p: config.top_p,
      frequency_penalty: config.frequency_penalty,
      presence_penalty: config.presence_penalty,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No content generated');
    }

    return content;
  } catch (error) {
    console.error('Qwen API error:', error);
    throw new Error('Failed to generate content');
  }
}

// 生成SEO信息的函数
export async function generateSEOInfo(prompt: string, config = AI_CONFIG) {
  try {
    const response = await qwen.chat.completions.create({
      model: config.model,
      messages: [
        {
          role: 'system',
          content: `你是一个SEO专家，擅长为博文生成优化的SEO信息。请严格按照JSON格式返回结果。`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.3, // 降低温度以获得更一致的结果
      max_tokens: 1000,
      top_p: config.top_p,
      frequency_penalty: config.frequency_penalty,
      presence_penalty: config.presence_penalty,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No SEO content generated');
    }

    return content;
  } catch (error) {
    console.error('Qwen API error:', error);
    throw new Error('Failed to generate SEO info');
  }
}

// 生成系列总结的函数
export async function generateSeriesSummary(prompt: string, config = AI_CONFIG) {
  try {
    const response = await qwen.chat.completions.create({
      model: config.model,
      messages: [
        {
          role: 'system',
          content: `你是一个内容总结专家，擅长为博文系列生成简洁而全面的总结。`
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.5,
      max_tokens: 500,
      top_p: config.top_p,
      frequency_penalty: config.frequency_penalty,
      presence_penalty: config.presence_penalty,
    });

    const content = response.choices[0]?.message?.content;
    if (!content) {
      throw new Error('No summary generated');
    }

    return content;
  } catch (error) {
    console.error('Qwen API error:', error);
    throw new Error('Failed to generate series summary');
  }
}
