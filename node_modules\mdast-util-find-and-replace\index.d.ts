export { findAndReplace } from "./lib/index.js";
export type FindAndReplaceList = import("./lib/index.js").FindAndReplaceList;
export type FindAndReplaceTuple = import("./lib/index.js").FindAndReplaceTuple;
export type Find = import("./lib/index.js").Find;
export type Options = import("./lib/index.js").Options;
export type RegExpMatchObject = import("./lib/index.js").RegExpMatchObject;
export type ReplaceFunction = import("./lib/index.js").ReplaceFunction;
export type Replace = import("./lib/index.js").Replace;
//# sourceMappingURL=index.d.ts.map