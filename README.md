# AI博文自动生成系统

一个基于AI的智能博文生成系统，支持多语言、SEO优化、系列管理等功能。

## 功能特性

### 🤖 AI博文生成
- 支持基于关键词、话题、标题生成博文
- 多语言支持（中文、英文、日文、韩文等）
- 智能SEO优化
- 自动生成标题、摘要、标签

### 📚 系列管理
- 创建和管理博文系列
- 自动维护系列文章的关联性
- 智能生成系列历史总结
- 为新文章提供上下文参考

### ✏️ 编辑预览
- 实时Markdown预览
- 富文本编辑器
- SEO信息编辑
- 一键发布功能

### 👤 作者管理
- 多作者支持
- 作者信息管理
- 社交媒体链接
- 头像和简介设置

### 🔧 Prompt调试
- 自定义AI提示词模板
- 实时测试和调试
- 模板变量支持
- 性能监控

### 📊 审核发布
- 博文状态管理
- 批量操作
- 筛选和搜索
- 一键发布到数据库

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript
- **UI组件**: Tailwind CSS, shadcn/ui
- **数据库**: Supabase (PostgreSQL)
- **AI服务**: Qwen API (阿里云通义千问)
- **部署**: Vercel (推荐)

## 快速开始

### 1. 环境准备

确保你已安装：
- Node.js 18+
- npm 或 yarn

### 2. 克隆项目

```bash
git clone <repository-url>
cd blog-auto-v2
```

### 3. 安装依赖

```bash
npm install
```

### 4. 环境配置

复制 `.env.local` 文件并填入你的配置：

```env
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Qwen API配置
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 5. 数据库设置

**重要：现在需要在Supabase中创建数据库表**

1. 访问 [Supabase控制台](https://supabase.com/dashboard)
2. 选择项目：`ohcnehqjrqzjlgbmjcck`
3. 点击左侧菜单的 "SQL Editor"
4. 点击 "New query" 创建新查询
5. 复制 `database/init.sql` 文件的全部内容并粘贴
6. 点击 "Run" 执行脚本

详细说明请参考 `database/SETUP.md`

### 6. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 使用指南

### 生成博文

1. 在"生成博文"标签页中选择生成类型（关键词/话题/标题）
2. 输入相关内容和语言设置
3. 可选择博文系列或创建独立文章
4. 点击"生成博文"等待AI生成内容
5. 在"预览"标签页查看生成结果

### 管理系列

1. 在"系列管理"标签页创建新系列
2. 设置系列名称、描述和语言
3. 生成博文时选择对应系列
4. 系统会自动维护系列文章的关联性

### 审核发布

1. 在"审核管理"标签页查看所有博文
2. 使用筛选器按状态、作者、语言筛选
3. 预览博文内容和SEO信息
4. 一键发布草稿文章

### Prompt调试

1. 在"Prompt调试"标签页创建或编辑模板
2. 设置模板变量和类型
3. 实时测试模板效果
4. 监控生成性能和质量

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   └── page.tsx           # 主页面
├── components/            # React组件
│   ├── blog/             # 博文相关组件
│   ├── series/           # 系列管理组件
│   ├── author/           # 作者管理组件
│   ├── prompt/           # Prompt调试组件
│   └── ui/               # UI基础组件
├── lib/                  # 工具库
│   ├── ai/              # AI服务
│   └── supabase/        # 数据库服务
└── types/               # TypeScript类型定义
```

## 部署

### Vercel部署（推荐）

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 设置环境变量
4. 部署完成

### 其他平台

项目是标准的Next.js应用，可以部署到任何支持Node.js的平台。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
