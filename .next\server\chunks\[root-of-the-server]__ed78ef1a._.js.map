{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/ai/openai.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nif (!process.env.OPENAI_API_KEY) {\n  throw new Error('Missing OpenAI API key');\n}\n\nexport const openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// 默认的AI配置\nexport const AI_CONFIG = {\n  model: 'gpt-4o-mini', // 使用更经济的模型\n  temperature: 0.7,\n  max_tokens: 4000,\n  top_p: 1,\n  frequency_penalty: 0,\n  presence_penalty: 0,\n};\n\n// 生成博文的函数\nexport async function generateBlogPost(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个专业的博客写手，擅长创作高质量、SEO友好的博文内容。请严格按照用户要求的格式返回结果。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: config.temperature,\n      max_tokens: config.max_tokens,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No content generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate content');\n  }\n}\n\n// 生成SEO信息的函数\nexport async function generateSEOInfo(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个SEO专家，擅长为博文生成优化的SEO信息。请严格按照JSON格式返回结果。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.3, // 降低温度以获得更一致的结果\n      max_tokens: 1000,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No SEO content generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate SEO info');\n  }\n}\n\n// 生成系列总结的函数\nexport async function generateSeriesSummary(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个内容总结专家，擅长为博文系列生成简洁而全面的总结。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.5,\n      max_tokens: 500,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No summary generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate series summary');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC/B,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAGO,MAAM,YAAY;IACvB,OAAO;IACP,aAAa;IACb,YAAY;IACZ,OAAO;IACP,mBAAmB;IACnB,kBAAkB;AACpB;AAGO,eAAe,iBAAiB,MAAc,EAAE,SAAS,SAAS;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,gDAAgD,CAAC;gBAC7D;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa,OAAO,WAAW;YAC/B,YAAY,OAAO,UAAU;YAC7B,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,gBAAgB,MAAc,EAAE,SAAS,SAAS;IACtE,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,0CAA0C,CAAC;gBACvD;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;YACZ,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,sBAAsB,MAAc,EAAE,SAAS,SAAS;IAC5E,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,6BAA6B,CAAC;gBAC1C;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;YACZ,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport type { Database } from './types';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n// 检查环境变量是否配置\nconst isConfigured = supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_url_here' &&\n  supabaseAnonKey !== 'your_supabase_anon_key_here';\n\n// 如果未配置，使用模拟客户端\nexport const supabase = isConfigured\n  ? createClient<Database>(supabaseUrl!, supabaseAnonKey!)\n  : null;\n\n// 服务端客户端（用于服务端操作）\nexport const createServerClient = () => {\n  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n\n  if (!isConfigured || !supabaseServiceKey || supabaseServiceKey === 'your_supabase_service_role_key_here') {\n    return null;\n  }\n\n  return createClient<Database>(supabaseUrl!, supabaseServiceKey);\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM;AACN,MAAM;AAEN,aAAa;AACb,MAAM,eAAe,eACnB,mBACA,gBAAgB,4BAChB,oBAAoB;AAGf,MAAM,WAAW,uCACpB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAc,mBACrC;AAGG,MAAM,qBAAqB;IAChC,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;IAEhE,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,uBAAuB,uCAAuC;QACxG,OAAO;IACT;IAEA,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAc;AAC9C", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/supabase/services.ts"], "sourcesContent": ["import { supabase, createServerClient } from './client';\nimport type { Database } from './types';\nimport type { BlogPost, BlogSeries, Author, PromptTemplate } from '@/types/blog';\n\ntype Tables = Database['public']['Tables'];\n\n// 检查Supabase是否已配置\nconst checkSupabaseConfig = () => {\n  if (!supabase) {\n    throw new Error('Supabase未配置。请在.env.local文件中设置正确的Supabase环境变量。');\n  }\n};\n\n// 作者相关操作\nexport const authorService = {\n  async getAll() {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data as Author[];\n  },\n\n  async getById(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data as Author;\n  },\n\n  async create(author: Tables['authors']['Insert']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .insert(author)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Author;\n  },\n\n  async update(id: string, updates: Tables['authors']['Update']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Author;\n  },\n\n  async delete(id: string) {\n    checkSupabaseConfig();\n    const { error } = await supabase!\n      .from('authors')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n};\n\n// 博文系列相关操作\nexport const seriesService = {\n  async getAll(authorId?: string) {\n    checkSupabaseConfig();\n    let query = supabase!\n      .from('blog_series')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (authorId) {\n      query = query.eq('author_id', authorId);\n    }\n\n    const { data, error } = await query;\n    if (error) throw error;\n    return data as BlogSeries[];\n  },\n\n  async getById(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_series')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data as BlogSeries;\n  },\n\n  async create(series: Tables['blog_series']['Insert']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_series')\n      .insert(series)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogSeries;\n  },\n\n  async update(id: string, updates: Tables['blog_series']['Update']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_series')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogSeries;\n  },\n\n  async delete(id: string) {\n    checkSupabaseConfig();\n    const { error } = await supabase!\n      .from('blog_series')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n\n  async getPostsInSeries(seriesId: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .select('*')\n      .eq('series_id', seriesId)\n      .order('series_order', { ascending: true });\n\n    if (error) throw error;\n    return data as BlogPost[];\n  }\n};\n\n// 博文相关操作\nexport const blogService = {\n  async getAll(filters?: {\n    authorId?: string;\n    seriesId?: string;\n    status?: 'draft' | 'published' | 'archived';\n    language?: string;\n  }) {\n    checkSupabaseConfig();\n    let query = supabase!\n      .from('blog_posts')\n      .select(`\n        *,\n        authors(name, avatar_url),\n        blog_series(name)\n      `)\n      .order('created_at', { ascending: false });\n\n    if (filters?.authorId) query = query.eq('author_id', filters.authorId);\n    if (filters?.seriesId) query = query.eq('series_id', filters.seriesId);\n    if (filters?.status) query = query.eq('status', filters.status);\n    if (filters?.language) query = query.eq('language', filters.language);\n\n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n  },\n\n  async getById(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .select(`\n        *,\n        authors(name, avatar_url),\n        blog_series(name)\n      `)\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data;\n  },\n\n  async create(post: Tables['blog_posts']['Insert']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .insert(post)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogPost;\n  },\n\n  async update(id: string, updates: Tables['blog_posts']['Update']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogPost;\n  },\n\n  async delete(id: string) {\n    checkSupabaseConfig();\n    const { error } = await supabase!\n      .from('blog_posts')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n\n  async publish(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .update({\n        status: 'published',\n        published_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogPost;\n  }\n};\n\n// Prompt模板相关操作\nexport const promptService = {\n  async getAll(type?: 'generation' | 'seo' | 'summary') {\n    checkSupabaseConfig();\n    let query = supabase!\n      .from('prompt_templates')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (type) {\n      query = query.eq('type', type);\n    }\n\n    const { data, error } = await query;\n    if (error) throw error;\n    return data as PromptTemplate[];\n  },\n\n  async getById(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('prompt_templates')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data as PromptTemplate;\n  },\n\n  async create(template: Tables['prompt_templates']['Insert']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('prompt_templates')\n      .insert(template)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as PromptTemplate;\n  },\n\n  async update(id: string, updates: Tables['prompt_templates']['Update']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('prompt_templates')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as PromptTemplate;\n  },\n\n  async delete(id: string) {\n    checkSupabaseConfig();\n    const { error } = await supabase!\n      .from('prompt_templates')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAMA,kBAAkB;AAClB,MAAM,sBAAsB;IAC1B,IAAI,CAAC,kIAAA,CAAA,WAAQ,EAAE;QACb,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM;QACJ;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,MAAmC;QAC9C;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAAoC;QAC3D;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,QAAO,QAAiB;QAC5B;QACA,IAAI,QAAQ,kIAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,eACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,UAAU;YACZ,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,MAAuC;QAClD;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAAwC;QAC/D;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,eACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,kBAAiB,QAAgB;QACrC;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,KAAK,CAAC,gBAAgB;YAAE,WAAW;QAAK;QAE3C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,OAKZ;QACC;QACA,IAAI,QAAQ,kIAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,SAAS,UAAU,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,QAAQ;QACrE,IAAI,SAAS,UAAU,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,QAAQ;QACrE,IAAI,SAAS,QAAQ,QAAQ,MAAM,EAAE,CAAC,UAAU,QAAQ,MAAM;QAC9D,IAAI,SAAS,UAAU,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;QAEpE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,IAAoC;QAC/C;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,MACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAAuC;QAC9D;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC;YACN,QAAQ;YACR,cAAc,IAAI,OAAO,WAAW;QACtC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,QAAO,IAAuC;QAClD;QACA,IAAI,QAAQ,kIAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,MAAM;YACR,QAAQ,MAAM,EAAE,CAAC,QAAQ;QAC3B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,QAA8C;QACzD;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAA6C;QACpE;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,oBACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/ai/blog-generator.ts"], "sourcesContent": ["import { generateBlogPost, generateSEOInfo, generateSeriesSummary } from './openai';\nimport { promptService, seriesService, blogService } from '../supabase/services';\nimport type { BlogGenerationRequest, BlogGenerationResponse, Language } from '@/types/blog';\n\n// 替换模板变量的辅助函数\nfunction replaceTemplateVariables(template: string, variables: Record<string, string>): string {\n  let result = template;\n  for (const [key, value] of Object.entries(variables)) {\n    result = result.replace(new RegExp(`{${key}}`, 'g'), value);\n  }\n  return result;\n}\n\n// 解析JSON响应的辅助函数\nfunction parseJSONResponse(content: string): any {\n  try {\n    // 尝试直接解析\n    return JSON.parse(content);\n  } catch {\n    // 如果失败，尝试提取JSON部分\n    const jsonMatch = content.match(/```json\\s*([\\s\\S]*?)\\s*```/) || content.match(/{[\\s\\S]*}/);\n    if (jsonMatch) {\n      try {\n        return JSON.parse(jsonMatch[1] || jsonMatch[0]);\n      } catch {\n        throw new Error('Failed to parse JSON response');\n      }\n    }\n    throw new Error('No valid JSON found in response');\n  }\n}\n\n// 获取系列上下文信息\nasync function getSeriesContext(seriesId: string): Promise<string> {\n  try {\n    const series = await seriesService.getById(seriesId);\n    const posts = await seriesService.getPostsInSeries(seriesId);\n    \n    if (posts.length === 0) {\n      return `这是系列\"${series.name}\"的第一篇文章。系列描述：${series.description}`;\n    }\n    \n    const publishedPosts = posts.filter(post => post.status === 'published');\n    const postSummaries = publishedPosts.map((post, index) => \n      `${index + 1}. ${post.title}: ${post.summary || '暂无摘要'}`\n    ).join('\\n');\n    \n    return `\n系列背景：${series.name} - ${series.description}\n系列总结：${series.summary || '暂无总结'}\n已发布文章：\n${postSummaries}\n\n请基于以上系列背景和已发布文章，生成与系列主题相关且有逻辑连续性的新文章。`;\n  } catch (error) {\n    console.error('Error getting series context:', error);\n    return '';\n  }\n}\n\n// 主要的博文生成函数\nexport async function generateBlog(request: BlogGenerationRequest): Promise<BlogGenerationResponse> {\n  try {\n    // 获取生成模板\n    const templates = await promptService.getAll('generation');\n    const template = templates[0]; // 使用第一个生成模板\n    \n    if (!template) {\n      throw new Error('No generation template found');\n    }\n    \n    // 准备模板变量\n    const variables: Record<string, string> = {\n      input_type: request.type === 'keyword' ? '关键词' : request.type === 'topic' ? '话题' : '标题',\n      input_content: request.input,\n      language: request.language,\n      series_context: request.series_id ? await getSeriesContext(request.series_id) : '这是一篇独立的博文。'\n    };\n    \n    // 如果有额外指令，添加到模板中\n    let finalTemplate = template.template;\n    if (request.additional_instructions) {\n      finalTemplate += `\\n\\n额外要求：${request.additional_instructions}`;\n    }\n    \n    // 替换模板变量\n    const prompt = replaceTemplateVariables(finalTemplate, variables);\n    \n    // 生成博文内容\n    const response = await generateBlogPost(prompt);\n    const parsedResponse = parseJSONResponse(response);\n    \n    // 验证响应格式\n    if (!parsedResponse.title || !parsedResponse.content) {\n      throw new Error('Invalid response format: missing title or content');\n    }\n    \n    // 生成SEO信息\n    const seoResponse = await generateSEOForBlog({\n      title: parsedResponse.title,\n      content: parsedResponse.content,\n      language: request.language\n    });\n    \n    return {\n      title: parsedResponse.title,\n      content: parsedResponse.content,\n      summary: parsedResponse.summary || '',\n      seo_title: seoResponse.seo_title,\n      seo_description: seoResponse.seo_description,\n      seo_keywords: seoResponse.seo_keywords,\n      tags: seoResponse.tags,\n      category: seoResponse.category\n    };\n    \n  } catch (error) {\n    console.error('Blog generation error:', error);\n    throw new Error(`Failed to generate blog: ${error instanceof Error ? error.message : 'Unknown error'}`);\n  }\n}\n\n// 为博文生成SEO信息\nexport async function generateSEOForBlog(params: {\n  title: string;\n  content: string;\n  language: string;\n}): Promise<{\n  seo_title: string;\n  seo_description: string;\n  seo_keywords: string[];\n  tags: string[];\n  category: string;\n}> {\n  try {\n    // 获取SEO模板\n    const templates = await promptService.getAll('seo');\n    const template = templates[0];\n    \n    if (!template) {\n      throw new Error('No SEO template found');\n    }\n    \n    // 准备模板变量\n    const variables = {\n      title: params.title,\n      content: params.content.substring(0, 2000), // 限制内容长度\n      language: params.language\n    };\n    \n    // 替换模板变量\n    const prompt = replaceTemplateVariables(template.template, variables);\n    \n    // 生成SEO信息\n    const response = await generateSEOInfo(prompt);\n    const parsedResponse = parseJSONResponse(response);\n    \n    return {\n      seo_title: parsedResponse.seo_title || params.title,\n      seo_description: parsedResponse.seo_description || '',\n      seo_keywords: Array.isArray(parsedResponse.seo_keywords) ? parsedResponse.seo_keywords : [],\n      tags: Array.isArray(parsedResponse.tags) ? parsedResponse.tags : [],\n      category: parsedResponse.category || '未分类'\n    };\n    \n  } catch (error) {\n    console.error('SEO generation error:', error);\n    // 返回默认值而不是抛出错误\n    return {\n      seo_title: params.title,\n      seo_description: '',\n      seo_keywords: [],\n      tags: [],\n      category: '未分类'\n    };\n  }\n}\n\n// 更新系列总结\nexport async function updateSeriesSummary(seriesId: string): Promise<void> {\n  try {\n    const series = await seriesService.getById(seriesId);\n    const posts = await seriesService.getPostsInSeries(seriesId);\n    const publishedPosts = posts.filter(post => post.status === 'published');\n    \n    if (publishedPosts.length === 0) {\n      return; // 没有已发布的文章，不需要更新总结\n    }\n    \n    // 获取总结模板\n    const templates = await promptService.getAll('summary');\n    const template = templates[0];\n    \n    if (!template) {\n      console.warn('No summary template found');\n      return;\n    }\n    \n    // 准备文章列表\n    const postsList = publishedPosts.map((post, index) => \n      `${index + 1}. 《${post.title}》\\n   摘要：${post.summary || '暂无摘要'}\\n   主要内容：${post.content.substring(0, 200)}...`\n    ).join('\\n\\n');\n    \n    // 准备模板变量\n    const variables = {\n      series_name: series.name,\n      series_description: series.description || '',\n      published_posts: postsList\n    };\n    \n    // 替换模板变量\n    const prompt = replaceTemplateVariables(template.template, variables);\n    \n    // 生成总结\n    const summary = await generateSeriesSummary(prompt);\n    \n    // 更新系列总结\n    await seriesService.update(seriesId, { summary });\n    \n  } catch (error) {\n    console.error('Series summary update error:', error);\n    // 不抛出错误，因为这不是关键功能\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGA,cAAc;AACd,SAAS,yBAAyB,QAAgB,EAAE,SAAiC;IACnF,IAAI,SAAS;IACb,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,WAAY;QACpD,SAAS,OAAO,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM;IACvD;IACA,OAAO;AACT;AAEA,gBAAgB;AAChB,SAAS,kBAAkB,OAAe;IACxC,IAAI;QACF,SAAS;QACT,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAM;QACN,kBAAkB;QAClB,MAAM,YAAY,QAAQ,KAAK,CAAC,iCAAiC,QAAQ,KAAK,CAAC;QAC/E,IAAI,WAAW;YACb,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE;YAChD,EAAE,OAAM;gBACN,MAAM,IAAI,MAAM;YAClB;QACF;QACA,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,YAAY;AACZ,eAAe,iBAAiB,QAAgB;IAC9C,IAAI;QACF,MAAM,SAAS,MAAM,oIAAA,CAAA,gBAAa,CAAC,OAAO,CAAC;QAC3C,MAAM,QAAQ,MAAM,oIAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;QAEnD,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,OAAO,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,aAAa,EAAE,OAAO,WAAW,EAAE;QAChE;QAEA,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QAC5D,MAAM,gBAAgB,eAAe,GAAG,CAAC,CAAC,MAAM,QAC9C,GAAG,QAAQ,EAAE,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,OAAO,IAAI,QAAQ,EACxD,IAAI,CAAC;QAEP,OAAO,CAAC;KACP,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,OAAO,WAAW,CAAC;KACtC,EAAE,OAAO,OAAO,IAAI,OAAO;;AAEhC,EAAE,cAAc;;qCAEqB,CAAC;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO;IACT;AACF;AAGO,eAAe,aAAa,OAA8B;IAC/D,IAAI;QACF,SAAS;QACT,MAAM,YAAY,MAAM,oIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAC7C,MAAM,WAAW,SAAS,CAAC,EAAE,EAAE,YAAY;QAE3C,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS;QACT,MAAM,YAAoC;YACxC,YAAY,QAAQ,IAAI,KAAK,YAAY,QAAQ,QAAQ,IAAI,KAAK,UAAU,OAAO;YACnF,eAAe,QAAQ,KAAK;YAC5B,UAAU,QAAQ,QAAQ;YAC1B,gBAAgB,QAAQ,SAAS,GAAG,MAAM,iBAAiB,QAAQ,SAAS,IAAI;QAClF;QAEA,iBAAiB;QACjB,IAAI,gBAAgB,SAAS,QAAQ;QACrC,IAAI,QAAQ,uBAAuB,EAAE;YACnC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,uBAAuB,EAAE;QAChE;QAEA,SAAS;QACT,MAAM,SAAS,yBAAyB,eAAe;QAEvD,SAAS;QACT,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE;QACxC,MAAM,iBAAiB,kBAAkB;QAEzC,SAAS;QACT,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,eAAe,OAAO,EAAE;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,UAAU;QACV,MAAM,cAAc,MAAM,mBAAmB;YAC3C,OAAO,eAAe,KAAK;YAC3B,SAAS,eAAe,OAAO;YAC/B,UAAU,QAAQ,QAAQ;QAC5B;QAEA,OAAO;YACL,OAAO,eAAe,KAAK;YAC3B,SAAS,eAAe,OAAO;YAC/B,SAAS,eAAe,OAAO,IAAI;YACnC,WAAW,YAAY,SAAS;YAChC,iBAAiB,YAAY,eAAe;YAC5C,cAAc,YAAY,YAAY;YACtC,MAAM,YAAY,IAAI;YACtB,UAAU,YAAY,QAAQ;QAChC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;IACxG;AACF;AAGO,eAAe,mBAAmB,MAIxC;IAOC,IAAI;QACF,UAAU;QACV,MAAM,YAAY,MAAM,oIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAC7C,MAAM,WAAW,SAAS,CAAC,EAAE;QAE7B,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,SAAS;QACT,MAAM,YAAY;YAChB,OAAO,OAAO,KAAK;YACnB,SAAS,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG;YACrC,UAAU,OAAO,QAAQ;QAC3B;QAEA,SAAS;QACT,MAAM,SAAS,yBAAyB,SAAS,QAAQ,EAAE;QAE3D,UAAU;QACV,MAAM,WAAW,MAAM,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE;QACvC,MAAM,iBAAiB,kBAAkB;QAEzC,OAAO;YACL,WAAW,eAAe,SAAS,IAAI,OAAO,KAAK;YACnD,iBAAiB,eAAe,eAAe,IAAI;YACnD,cAAc,MAAM,OAAO,CAAC,eAAe,YAAY,IAAI,eAAe,YAAY,GAAG,EAAE;YAC3F,MAAM,MAAM,OAAO,CAAC,eAAe,IAAI,IAAI,eAAe,IAAI,GAAG,EAAE;YACnE,UAAU,eAAe,QAAQ,IAAI;QACvC;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,eAAe;QACf,OAAO;YACL,WAAW,OAAO,KAAK;YACvB,iBAAiB;YACjB,cAAc,EAAE;YAChB,MAAM,EAAE;YACR,UAAU;QACZ;IACF;AACF;AAGO,eAAe,oBAAoB,QAAgB;IACxD,IAAI;QACF,MAAM,SAAS,MAAM,oIAAA,CAAA,gBAAa,CAAC,OAAO,CAAC;QAC3C,MAAM,QAAQ,MAAM,oIAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;QACnD,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QAE5D,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,QAAQ,mBAAmB;QAC7B;QAEA,SAAS;QACT,MAAM,YAAY,MAAM,oIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAC7C,MAAM,WAAW,SAAS,CAAC,EAAE;QAE7B,IAAI,CAAC,UAAU;YACb,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,SAAS;QACT,MAAM,YAAY,eAAe,GAAG,CAAC,CAAC,MAAM,QAC1C,GAAG,QAAQ,EAAE,GAAG,EAAE,KAAK,KAAK,CAAC,SAAS,EAAE,KAAK,OAAO,IAAI,OAAO,UAAU,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,EAC9G,IAAI,CAAC;QAEP,SAAS;QACT,MAAM,YAAY;YAChB,aAAa,OAAO,IAAI;YACxB,oBAAoB,OAAO,WAAW,IAAI;YAC1C,iBAAiB;QACnB;QAEA,SAAS;QACT,MAAM,SAAS,yBAAyB,SAAS,QAAQ,EAAE;QAE3D,OAAO;QACP,MAAM,UAAU,MAAM,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;QAE5C,SAAS;QACT,MAAM,oIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU;YAAE;QAAQ;IAEjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAC9C,kBAAkB;IACpB;AACF", "debugId": null}}, {"offset": {"line": 667, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/api/blog/generate/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { generateBlog, updateSeriesSummary } from '@/lib/ai/blog-generator';\nimport { blogService } from '@/lib/supabase/services';\nimport type { BlogGenerationRequest } from '@/types/blog';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body: BlogGenerationRequest = await request.json();\n    \n    // 验证请求数据\n    if (!body.input || !body.language || !body.author_id) {\n      return NextResponse.json(\n        { error: 'Missing required fields: input, language, author_id' },\n        { status: 400 }\n      );\n    }\n    \n    if (!['keyword', 'topic', 'title'].includes(body.type)) {\n      return NextResponse.json(\n        { error: 'Invalid type. Must be keyword, topic, or title' },\n        { status: 400 }\n      );\n    }\n    \n    // 生成博文\n    const generatedBlog = await generateBlog(body);\n    \n    // 创建博文记录\n    const blogPost = await blogService.create({\n      title: generatedBlog.title,\n      content: generatedBlog.content,\n      summary: generatedBlog.summary,\n      language: body.language,\n      seo_title: generatedBlog.seo_title,\n      seo_description: generatedBlog.seo_description,\n      seo_keywords: generatedBlog.seo_keywords,\n      tags: generatedBlog.tags,\n      category: generatedBlog.category,\n      author_id: body.author_id,\n      series_id: body.series_id || null,\n      series_order: body.series_id ? await getNextSeriesOrder(body.series_id) : null,\n      status: 'draft'\n    });\n    \n    // 如果是系列文章，更新系列总结\n    if (body.series_id) {\n      // 异步更新，不阻塞响应\n      updateSeriesSummary(body.series_id).catch(error => \n        console.error('Failed to update series summary:', error)\n      );\n    }\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        blog_post: blogPost,\n        generated_content: generatedBlog\n      }\n    });\n    \n  } catch (error) {\n    console.error('Blog generation API error:', error);\n    return NextResponse.json(\n      { \n        error: 'Failed to generate blog',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// 获取系列中下一个文章的顺序号\nasync function getNextSeriesOrder(seriesId: string): Promise<number> {\n  try {\n    const posts = await blogService.getAll({ seriesId });\n    const maxOrder = posts.reduce((max, post) => \n      Math.max(max, post.series_order || 0), 0\n    );\n    return maxOrder + 1;\n  } catch (error) {\n    console.error('Error getting next series order:', error);\n    return 1;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAA8B,MAAM,QAAQ,IAAI;QAEtD,SAAS;QACT,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,SAAS,EAAE;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsD,GAC/D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC;YAAC;YAAW;YAAS;SAAQ,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiD,GAC1D;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,gBAAgB,MAAM,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAAE;QAEzC,SAAS;QACT,MAAM,WAAW,MAAM,oIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACxC,OAAO,cAAc,KAAK;YAC1B,SAAS,cAAc,OAAO;YAC9B,SAAS,cAAc,OAAO;YAC9B,UAAU,KAAK,QAAQ;YACvB,WAAW,cAAc,SAAS;YAClC,iBAAiB,cAAc,eAAe;YAC9C,cAAc,cAAc,YAAY;YACxC,MAAM,cAAc,IAAI;YACxB,UAAU,cAAc,QAAQ;YAChC,WAAW,KAAK,SAAS;YACzB,WAAW,KAAK,SAAS,IAAI;YAC7B,cAAc,KAAK,SAAS,GAAG,MAAM,mBAAmB,KAAK,SAAS,IAAI;YAC1E,QAAQ;QACV;QAEA,iBAAiB;QACjB,IAAI,KAAK,SAAS,EAAE;YAClB,aAAa;YACb,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,SAAS,EAAE,KAAK,CAAC,CAAA,QACxC,QAAQ,KAAK,CAAC,oCAAoC;QAEtD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,WAAW;gBACX,mBAAmB;YACrB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,iBAAiB;AACjB,eAAe,mBAAmB,QAAgB;IAChD,IAAI;QACF,MAAM,QAAQ,MAAM,oIAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YAAE;QAAS;QAClD,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,OAClC,KAAK,GAAG,CAAC,KAAK,KAAK,YAAY,IAAI,IAAI;QAEzC,OAAO,WAAW;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF", "debugId": null}}]}