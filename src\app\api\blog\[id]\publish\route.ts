import { NextRequest, NextResponse } from 'next/server';
import { blogService, seriesService } from '@/lib/supabase/services';
import { updateSeriesSummary } from '@/lib/ai/blog-generator';

// 发布博文
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 获取博文信息
    const blogPost = await blogService.getById(params.id);
    
    if (blogPost.status === 'published') {
      return NextResponse.json(
        { error: 'Blog post is already published' },
        { status: 400 }
      );
    }
    
    // 发布博文
    const publishedPost = await blogService.publish(params.id);
    
    // 如果是系列文章，更新系列总结
    if (publishedPost.series_id) {
      // 异步更新，不阻塞响应
      updateSeriesSummary(publishedPost.series_id).catch(error => 
        console.error('Failed to update series summary:', error)
      );
    }
    
    return NextResponse.json({
      success: true,
      data: publishedPost,
      message: 'Blog post published successfully'
    });
    
  } catch (error) {
    console.error('Blog publish error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to publish blog post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
