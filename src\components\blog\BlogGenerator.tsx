'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SUPPORTED_LANGUAGES } from '@/types/blog';
import type { BlogGenerationRequest, Language, BlogSeries } from '@/types/blog';

interface BlogGeneratorProps {
  onGenerated?: (blogPost: any) => void;
}

export default function BlogGenerator({ onGenerated }: BlogGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [series, setSeries] = useState<BlogSeries[]>([]);
  const [formData, setFormData] = useState<Partial<BlogGenerationRequest>>({
    type: 'keyword',
    language: 'zh-CN',
    author_id: '00000000-0000-0000-0000-000000000001', // 默认作者ID
  });

  // 加载系列列表
  const loadSeries = async () => {
    try {
      const response = await fetch(`/api/series?author_id=${formData.author_id}`);
      const result = await response.json();

      if (result.success) {
        setSeries(result.data);
      }
    } catch (error) {
      console.error('Failed to load series:', error);
    }
  };

  useEffect(() => {
    if (formData.author_id) {
      loadSeries();
    }
  }, [formData.author_id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.input) {
      alert('请输入内容');
      return;
    }

    setIsGenerating(true);
    
    try {
      const response = await fetch('/api/blog/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Generation failed');
      }

      if (onGenerated) {
        onGenerated(result.data);
      }

      // 重置表单
      setFormData({
        ...formData,
        input: '',
        additional_instructions: '',
      });

      alert('博文生成成功！');
    } catch (error) {
      console.error('Generation error:', error);
      alert(`生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>AI博文生成器</CardTitle>
        <CardDescription>
          输入关键词、话题或标题，AI将为您生成高质量的博文内容
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 生成类型选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">生成类型</label>
            <Select
              value={formData.type}
              onValueChange={(value: 'keyword' | 'topic' | 'title') =>
                setFormData({ ...formData, type: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="keyword">关键词</SelectItem>
                <SelectItem value="topic">话题</SelectItem>
                <SelectItem value="title">标题</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* 输入内容 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {formData.type === 'keyword' && '关键词'}
              {formData.type === 'topic' && '话题描述'}
              {formData.type === 'title' && '文章标题'}
            </label>
            <Input
              value={formData.input || ''}
              onChange={(e) => setFormData({ ...formData, input: e.target.value })}
              placeholder={
                formData.type === 'keyword' ? '例如：人工智能, 机器学习' :
                formData.type === 'topic' ? '例如：人工智能在医疗领域的应用' :
                '例如：AI如何改变我们的生活'
              }
              required
            />
          </div>

          {/* 语言选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">语言</label>
            <Select
              value={formData.language}
              onValueChange={(value: Language) =>
                setFormData({ ...formData, language: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {SUPPORTED_LANGUAGES.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 系列选择 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">博文系列（可选）</label>
            <Select
              value={formData.series_id || 'none'}
              onValueChange={(value) =>
                setFormData({ ...formData, series_id: value === 'none' ? undefined : value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="选择系列或留空创建独立文章" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">独立文章</SelectItem>
                {series.map((seriesItem) => (
                  <SelectItem key={seriesItem.id} value={seriesItem.id}>
                    {seriesItem.name} ({seriesItem.posts_count} 篇文章)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 额外指令 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">额外指令（可选）</label>
            <Textarea
              value={formData.additional_instructions || ''}
              onChange={(e) =>
                setFormData({ ...formData, additional_instructions: e.target.value })
              }
              placeholder="例如：请重点关注实际应用案例，文章风格要轻松易懂"
              rows={3}
            />
          </div>

          {/* 提交按钮 */}
          <Button
            type="submit"
            disabled={isGenerating}
            className="w-full"
          >
            {isGenerating ? '正在生成...' : '生成博文'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
