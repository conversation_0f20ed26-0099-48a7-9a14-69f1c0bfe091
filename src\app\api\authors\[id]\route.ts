import { NextRequest, NextResponse } from 'next/server';
import { authorService } from '@/lib/supabase/services';

// 获取单个作者
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const author = await authorService.getById(params.id);
    
    return NextResponse.json({
      success: true,
      data: author
    });
    
  } catch (error) {
    console.error('Author fetch error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch author',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 更新作者
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    const author = await authorService.update(params.id, body);
    
    return NextResponse.json({
      success: true,
      data: author
    });
    
  } catch (error) {
    console.error('Author update error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update author',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 删除作者
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await authorService.delete(params.id);
    
    return NextResponse.json({
      success: true,
      message: 'Author deleted successfully'
    });
    
  } catch (error) {
    console.error('Author deletion error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete author',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
