module.exports = {

"[project]/.next-internal/server/app/api/series/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createServerClient": ()=>createServerClient,
    "supabase": ()=>supabase
});
;
const supabaseUrl = ("TURBOPACK compile-time value", "your_supabase_url_here");
const supabaseAnonKey = ("TURBOPACK compile-time value", "your_supabase_anon_key_here");
// 检查环境变量是否配置
const isConfigured = supabaseUrl && supabaseAnonKey && supabaseUrl !== 'your_supabase_url_here' && supabaseAnonKey !== 'your_supabase_anon_key_here';
const supabase = ("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : null;
const createServerClient = ()=>{
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if ("TURBOPACK compile-time truthy", 1) {
        return null;
    }
    //TURBOPACK unreachable
    ;
};
}),
"[project]/src/lib/supabase/services.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "authorService": ()=>authorService,
    "blogService": ()=>blogService,
    "promptService": ()=>promptService,
    "seriesService": ()=>seriesService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/client.ts [app-route] (ecmascript)");
;
// 检查Supabase是否已配置
const checkSupabaseConfig = ()=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"]) {
        throw new Error('Supabase未配置。请在.env.local文件中设置正确的Supabase环境变量。');
    }
};
const authorService = {
    async getAll () {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async getById (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (author) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').insert(author).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        checkSupabaseConfig();
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').delete().eq('id', id);
        if (error) throw error;
    }
};
const seriesService = {
    async getAll (authorId) {
        checkSupabaseConfig();
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').select('*').order('created_at', {
            ascending: false
        });
        if (authorId) {
            query = query.eq('author_id', authorId);
        }
        const { data, error } = await query;
        if (error) throw error;
        return data;
    },
    async getById (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (series) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').insert(series).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        checkSupabaseConfig();
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').delete().eq('id', id);
        if (error) throw error;
    },
    async getPostsInSeries (seriesId) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').select('*').eq('series_id', seriesId).order('series_order', {
            ascending: true
        });
        if (error) throw error;
        return data;
    }
};
const blogService = {
    async getAll (filters) {
        checkSupabaseConfig();
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').select(`
        *,
        authors(name, avatar_url),
        blog_series(name)
      `).order('created_at', {
            ascending: false
        });
        if (filters?.authorId) query = query.eq('author_id', filters.authorId);
        if (filters?.seriesId) query = query.eq('series_id', filters.seriesId);
        if (filters?.status) query = query.eq('status', filters.status);
        if (filters?.language) query = query.eq('language', filters.language);
        const { data, error } = await query;
        if (error) throw error;
        return data;
    },
    async getById (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').select(`
        *,
        authors(name, avatar_url),
        blog_series(name)
      `).eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (post) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').insert(post).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        checkSupabaseConfig();
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').delete().eq('id', id);
        if (error) throw error;
    },
    async publish (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').update({
            status: 'published',
            published_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
};
const promptService = {
    async getAll (type) {
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').select('*').order('created_at', {
            ascending: false
        });
        if (type) {
            query = query.eq('type', type);
        }
        const { data, error } = await query;
        if (error) throw error;
        return data;
    },
    async getById (id) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (template) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').insert(template).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').delete().eq('id', id);
        if (error) throw error;
    }
};
}),
"[project]/src/app/api/series/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/services.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const authorId = searchParams.get('author_id');
        const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["seriesService"].getAll(authorId || undefined);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: series
        });
    } catch (error) {
        console.error('Series fetch error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch series',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        // 验证请求数据
        if (!body.name || !body.author_id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields: name, author_id'
            }, {
                status: 400
            });
        }
        const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["seriesService"].create({
            name: body.name,
            description: body.description || null,
            language: body.language || 'zh-CN',
            author_id: body.author_id,
            summary: body.summary || null
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: series
        });
    } catch (error) {
        console.error('Series creation error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to create series',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c5e7883d._.js.map