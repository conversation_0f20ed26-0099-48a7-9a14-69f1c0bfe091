{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/ai/openai.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nif (!process.env.OPENAI_API_KEY) {\n  throw new Error('Missing OpenAI API key');\n}\n\nexport const openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// 默认的AI配置\nexport const AI_CONFIG = {\n  model: 'gpt-4o-mini', // 使用更经济的模型\n  temperature: 0.7,\n  max_tokens: 4000,\n  top_p: 1,\n  frequency_penalty: 0,\n  presence_penalty: 0,\n};\n\n// 生成博文的函数\nexport async function generateBlogPost(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个专业的博客写手，擅长创作高质量、SEO友好的博文内容。请严格按照用户要求的格式返回结果。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: config.temperature,\n      max_tokens: config.max_tokens,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No content generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate content');\n  }\n}\n\n// 生成SEO信息的函数\nexport async function generateSEOInfo(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个SEO专家，擅长为博文生成优化的SEO信息。请严格按照JSON格式返回结果。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.3, // 降低温度以获得更一致的结果\n      max_tokens: 1000,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No SEO content generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate SEO info');\n  }\n}\n\n// 生成系列总结的函数\nexport async function generateSeriesSummary(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个内容总结专家，擅长为博文系列生成简洁而全面的总结。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.5,\n      max_tokens: 500,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No summary generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate series summary');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC/B,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAGO,MAAM,YAAY;IACvB,OAAO;IACP,aAAa;IACb,YAAY;IACZ,OAAO;IACP,mBAAmB;IACnB,kBAAkB;AACpB;AAGO,eAAe,iBAAiB,MAAc,EAAE,SAAS,SAAS;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,gDAAgD,CAAC;gBAC7D;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa,OAAO,WAAW;YAC/B,YAAY,OAAO,UAAU;YAC7B,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,gBAAgB,MAAc,EAAE,SAAS,SAAS;IACtE,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,0CAA0C,CAAC;gBACvD;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;YACZ,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,sBAAsB,MAAc,EAAE,SAAS,SAAS;IAC5E,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,6BAA6B,CAAC;gBAC1C;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;YACZ,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/api/prompts/test/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { generateBlogPost, generateSEOInfo, generateSeriesSummary } from '@/lib/ai/openai';\n\n// 测试Prompt\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    \n    // 验证请求数据\n    if (!body.prompt || !body.type) {\n      return NextResponse.json(\n        { error: 'Missing required fields: prompt, type' },\n        { status: 400 }\n      );\n    }\n    \n    if (!['generation', 'seo', 'summary'].includes(body.type)) {\n      return NextResponse.json(\n        { error: 'Invalid type. Must be generation, seo, or summary' },\n        { status: 400 }\n      );\n    }\n    \n    let result: string;\n    const startTime = Date.now();\n    \n    try {\n      // 根据类型选择对应的生成函数\n      switch (body.type) {\n        case 'generation':\n          result = await generateBlogPost(body.prompt);\n          break;\n        case 'seo':\n          result = await generateSEOInfo(body.prompt);\n          break;\n        case 'summary':\n          result = await generateSeriesSummary(body.prompt);\n          break;\n        default:\n          throw new Error('Invalid prompt type');\n      }\n    } catch (aiError) {\n      return NextResponse.json(\n        { \n          error: 'AI generation failed',\n          details: aiError instanceof Error ? aiError.message : 'Unknown AI error'\n        },\n        { status: 500 }\n      );\n    }\n    \n    const endTime = Date.now();\n    const duration = endTime - startTime;\n    \n    return NextResponse.json({\n      success: true,\n      data: {\n        result,\n        duration,\n        prompt: body.prompt,\n        type: body.type,\n        timestamp: new Date().toISOString()\n      }\n    });\n    \n  } catch (error) {\n    console.error('Prompt test error:', error);\n    return NextResponse.json(\n      { \n        error: 'Failed to test prompt',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,SAAS;QACT,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,KAAK,IAAI,EAAE;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC;YAAC;YAAc;YAAO;SAAU,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoD,GAC7D;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QACJ,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,gBAAgB;YAChB,OAAQ,KAAK,IAAI;gBACf,KAAK;oBACH,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,MAAM;oBAC3C;gBACF,KAAK;oBACH,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM;oBAC1C;gBACF,KAAK;oBACH,SAAS,MAAM,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,MAAM;oBAChD;gBACF;oBACE,MAAM,IAAI,MAAM;YACpB;QACF,EAAE,OAAO,SAAS;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;gBACP,SAAS,mBAAmB,QAAQ,QAAQ,OAAO,GAAG;YACxD,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,UAAU,KAAK,GAAG;QACxB,MAAM,WAAW,UAAU;QAE3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA;gBACA,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI;gBACf,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}