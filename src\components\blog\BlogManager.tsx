'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Eye, Edit, Trash2, Search, Filter, CheckCircle, Clock, Archive } from 'lucide-react';
import BlogEditor from './BlogEditor';
import BlogPreview from './BlogPreview';

import type { BlogPost, Author } from '@/types/blog';

export default function BlogManager() {
  const [blogs, setBlogs] = useState<any[]>([]);
  const [authors, setAuthors] = useState<Author[]>([]);
  const [filteredBlogs, setFilteredBlogs] = useState<any[]>([]);
  const [selectedBlog, setSelectedBlog] = useState<any>(null);
  const [currentView, setCurrentView] = useState<'list' | 'preview' | 'edit'>('list');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [languageFilter, setLanguageFilter] = useState<string>('all');
  const [authorFilter, setAuthorFilter] = useState<string>('all');


  // 获取所有博文
  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (authorFilter !== 'all') params.append('author_id', authorFilter);
      if (languageFilter !== 'all') params.append('language', languageFilter);

      const response = await fetch(`/api/blog?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        setBlogs(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch blogs:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取作者列表
  const fetchAuthors = async () => {
    try {
      const response = await fetch('/api/authors');
      const result = await response.json();

      if (result.success) {
        setAuthors(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch authors:', error);
    }
  };

  useEffect(() => {
    fetchBlogs();
    fetchAuthors();
  }, [statusFilter, authorFilter, languageFilter]);

  // 过滤博文（仅用于搜索，其他过滤在API层面处理）
  useEffect(() => {
    let filtered = blogs;

    // 搜索过滤
    if (searchTerm) {
      filtered = filtered.filter(blog =>
        blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        blog.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (blog.summary && blog.summary.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    setFilteredBlogs(filtered);
  }, [blogs, searchTerm]);

  // 处理预览
  const handlePreview = (blog: any) => {
    setSelectedBlog(blog);
    setCurrentView('preview');
  };

  // 处理编辑
  const handleEdit = (blog: any) => {
    setSelectedBlog(blog);
    setCurrentView('edit');
  };

  // 处理保存
  const handleSave = async (updatedPost: Partial<BlogPost>) => {
    if (!selectedBlog) return;

    try {
      const response = await fetch(`/api/blog/${selectedBlog.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedPost),
      });

      if (!response.ok) {
        throw new Error('Failed to save');
      }

      const result = await response.json();
      
      // 更新本地状态
      const updatedBlogs = blogs.map(blog =>
        blog.id === selectedBlog.id ? { ...blog, ...updatedPost } : blog
      );
      setBlogs(updatedBlogs);
      setSelectedBlog({ ...selectedBlog, ...updatedPost });
      
      return result;
    } catch (error) {
      console.error('Save error:', error);
      throw error;
    }
  };

  // 处理发布
  const handlePublish = async (postId: string) => {
    try {
      const response = await fetch(`/api/blog/${postId}/publish`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Failed to publish');
      }

      const result = await response.json();
      
      // 更新本地状态
      const updatedBlogs = blogs.map(blog =>
        blog.id === postId
          ? { ...blog, status: 'published', published_at: new Date().toISOString() }
          : blog
      );
      setBlogs(updatedBlogs);

      if (selectedBlog && selectedBlog.id === postId) {
        setSelectedBlog({
          ...selectedBlog,
          status: 'published',
          published_at: new Date().toISOString()
        });
      }
      
      return result;
    } catch (error) {
      console.error('Publish error:', error);
      throw error;
    }
  };

  // 处理删除
  const handleDelete = async (blogId: string) => {
    if (!confirm('确定要删除这篇文章吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await fetch(`/api/blog/${blogId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete');
      }

      // 更新本地状态
      setBlogs(blogs.filter(blog => blog.id !== blogId));
      
      // 如果删除的是当前选中的文章，返回列表视图
      if (selectedBlog && selectedBlog.id === blogId) {
        setSelectedBlog(null);
        setCurrentView('list');
      }
      
      alert('文章删除成功！');
    } catch (error) {
      console.error('Delete error:', error);
      alert('删除失败，请重试。');
    }
  };

  // 返回列表
  const handleBackToList = () => {
    setSelectedBlog(null);
    setCurrentView('list');
  };

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />已发布</Badge>;
      case 'draft':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />草稿</Badge>;
      case 'archived':
        return <Badge variant="outline"><Archive className="w-3 h-3 mr-1" />已归档</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  // 列表视图
  if (currentView === 'list') {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold">文章管理</h2>
            <p className="text-gray-600">管理所有博文，支持预览、编辑和发布</p>
          </div>
          <div className="text-sm text-gray-500">
            共 {filteredBlogs.length} 篇文章
          </div>
        </div>

        {/* 搜索和过滤 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="w-5 h-5" />
              <span>搜索和过滤</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">搜索</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <Input
                    placeholder="搜索标题、内容或摘要..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">状态</label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="draft">草稿</SelectItem>
                    <SelectItem value="published">已发布</SelectItem>
                    <SelectItem value="archived">已归档</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">作者</label>
                <Select value={authorFilter} onValueChange={setAuthorFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部作者</SelectItem>
                    {authors.map((author) => (
                      <SelectItem key={author.id} value={author.id}>
                        {author.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">语言</label>
                <Select value={languageFilter} onValueChange={setLanguageFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部语言</SelectItem>
                    <SelectItem value="zh-CN">中文</SelectItem>
                    <SelectItem value="en-US">英文</SelectItem>
                    <SelectItem value="ja-JP">日文</SelectItem>
                    <SelectItem value="ko-KR">韩文</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 文章列表 */}
        <div className="space-y-4">
          {filteredBlogs.map((blog) => (
            <Card key={blog.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{blog.title}</CardTitle>
                    <div className="flex items-center space-x-2 mb-2">
                      {getStatusBadge(blog.status)}
                      <Badge variant="outline">{blog.language}</Badge>
                      {blog.blog_series?.name && (
                        <Badge variant="secondary">系列: {blog.blog_series.name}</Badge>
                      )}
                    </div>
                    <CardDescription>
                      {blog.summary || '暂无摘要'}
                    </CardDescription>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePreview(blog)}
                      className="flex items-center space-x-1"
                    >
                      <Eye className="w-4 h-4" />
                      <span>预览</span>
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(blog)}
                      className="flex items-center space-x-1"
                    >
                      <Edit className="w-4 h-4" />
                      <span>编辑</span>
                    </Button>

                    {blog.status === 'draft' && (
                      <Button
                        size="sm"
                        onClick={() => handlePublish(blog.id)}
                        className="flex items-center space-x-1"
                      >
                        <CheckCircle className="w-4 h-4" />
                        <span>发布</span>
                      </Button>
                    )}

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(blog.id)}
                      className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>删除</span>
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <span>作者: {blog.authors?.name}</span>
                    <span>创建: {new Date(blog.created_at).toLocaleDateString('zh-CN')}</span>
                    {blog.published_at && (
                      <span>发布: {new Date(blog.published_at).toLocaleDateString('zh-CN')}</span>
                    )}
                  </div>

                  {blog.tags && blog.tags.length > 0 && (
                    <div className="flex space-x-1">
                      {blog.tags.slice(0, 3).map((tag: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {blog.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{blog.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredBlogs.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">没有找到匹配的文章</p>
          </div>
        )}


      </div>
    );
  }

  // 预览视图
  if (currentView === 'preview' && selectedBlog) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Button variant="outline" onClick={handleBackToList}>
            ← 返回列表
          </Button>
          <Button onClick={() => setCurrentView('edit')}>
            <Edit className="w-4 h-4 mr-2" />
            编辑文章
          </Button>
        </div>
        
        <BlogPreview
          blogPost={selectedBlog}
          onEdit={() => setCurrentView('edit')}
        />
      </div>
    );
  }

  // 编辑视图
  if (currentView === 'edit' && selectedBlog) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Button variant="outline" onClick={handleBackToList}>
            ← 返回列表
          </Button>
          <Button variant="outline" onClick={() => setCurrentView('preview')}>
            <Eye className="w-4 h-4 mr-2" />
            预览文章
          </Button>
        </div>
        
        <BlogEditor
          blogPost={selectedBlog}
          onSave={handleSave}
          onPublish={handlePublish}
        />
      </div>
    );
  }

  return null;
}
