'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import type { BlogPost } from '@/types/blog';

interface BlogEditorProps {
  blogPost: BlogPost;
  onSave?: (updatedPost: Partial<BlogPost>) => void;
  onPublish?: (postId: string) => void;
}

export default function BlogEditor({ blogPost, onSave, onPublish }: BlogEditorProps) {
  const [editedPost, setEditedPost] = useState<Partial<BlogPost>>({
    title: blogPost.title,
    content: blogPost.content,
    summary: blogPost.summary || '',
    seo_title: blogPost.seo_title || '',
    seo_description: blogPost.seo_description || '',
    seo_keywords: blogPost.seo_keywords || [],
    tags: blogPost.tags || [],
    category: blogPost.category || '',
  });
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);

  // 处理保存
  const handleSave = async () => {
    setIsSaving(true);
    try {
      if (onSave) {
        await onSave(editedPost);
      }
      alert('保存成功！');
    } catch (error) {
      console.error('Save error:', error);
      alert('保存失败');
    } finally {
      setIsSaving(false);
    }
  };

  // 处理发布
  const handlePublish = async () => {
    if (!confirm('确定要发布这篇文章吗？')) {
      return;
    }

    setIsPublishing(true);
    try {
      if (onPublish) {
        await onPublish(blogPost.id);
      }
      alert('发布成功！');
    } catch (error) {
      console.error('Publish error:', error);
      alert('发布失败');
    } finally {
      setIsPublishing(false);
    }
  };

  // 处理关键词和标签的编辑
  const handleKeywordsChange = (value: string) => {
    const keywords = value.split(',').map(k => k.trim()).filter(k => k);
    setEditedPost({ ...editedPost, seo_keywords: keywords });
  };

  const handleTagsChange = (value: string) => {
    const tags = value.split(',').map(t => t.trim()).filter(t => t);
    setEditedPost({ ...editedPost, tags });
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* 头部操作栏 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">编辑博文</h1>
          <p className="text-gray-600">
            状态: {blogPost.status === 'draft' ? '草稿' : blogPost.status === 'published' ? '已发布' : '已归档'}
          </p>
        </div>
        <div className="space-x-2">
          <Button variant="outline" onClick={handleSave} disabled={isSaving}>
            {isSaving ? '保存中...' : '保存草稿'}
          </Button>
          {blogPost.status === 'draft' && (
            <Button onClick={handlePublish} disabled={isPublishing}>
              {isPublishing ? '发布中...' : '发布文章'}
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="edit" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="edit">编辑</TabsTrigger>
          <TabsTrigger value="preview">预览</TabsTrigger>
          <TabsTrigger value="seo">SEO设置</TabsTrigger>
        </TabsList>

        {/* 编辑标签页 */}
        <TabsContent value="edit" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>基本信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 标题 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">标题</label>
                <Input
                  value={editedPost.title || ''}
                  onChange={(e) => setEditedPost({ ...editedPost, title: e.target.value })}
                  placeholder="输入文章标题"
                />
              </div>

              {/* 摘要 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">摘要</label>
                <Textarea
                  value={editedPost.summary || ''}
                  onChange={(e) => setEditedPost({ ...editedPost, summary: e.target.value })}
                  placeholder="输入文章摘要"
                  rows={3}
                />
              </div>

              {/* 分类 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">分类</label>
                <Input
                  value={editedPost.category || ''}
                  onChange={(e) => setEditedPost({ ...editedPost, category: e.target.value })}
                  placeholder="输入文章分类"
                />
              </div>

              {/* 标签 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">标签（用逗号分隔）</label>
                <Input
                  value={(editedPost.tags || []).join(', ')}
                  onChange={(e) => handleTagsChange(e.target.value)}
                  placeholder="例如：AI, 机器学习, 技术"
                />
                <div className="flex flex-wrap gap-1">
                  {(editedPost.tags || []).map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 内容编辑 */}
          <Card>
            <CardHeader>
              <CardTitle>文章内容</CardTitle>
              <CardDescription>
                支持Markdown格式，可以使用**粗体**、*斜体*、# 标题等语法
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Textarea
                value={editedPost.content || ''}
                onChange={(e) => setEditedPost({ ...editedPost, content: e.target.value })}
                placeholder="在这里输入文章内容..."
                rows={20}
                className="font-mono"
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* 预览标签页 */}
        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{editedPost.title || '未命名文章'}</CardTitle>
              {editedPost.summary && (
                <CardDescription className="text-base">
                  {editedPost.summary}
                </CardDescription>
              )}
              <div className="flex items-center gap-2 text-sm text-gray-500">
                {editedPost.category && (
                  <>
                    <Badge variant="secondary">{editedPost.category}</Badge>
                    <span>•</span>
                  </>
                )}
                <span>语言: {blogPost.language}</span>
                <span>•</span>
                <span>创建时间: {new Date(blogPost.created_at).toLocaleDateString('zh-CN')}</span>
              </div>
              {(editedPost.tags || []).length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {(editedPost.tags || []).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {editedPost.content || ''}
                </ReactMarkdown>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* SEO设置标签页 */}
        <TabsContent value="seo" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>SEO优化设置</CardTitle>
              <CardDescription>
                优化搜索引擎排名的相关设置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* SEO标题 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">SEO标题</label>
                <Input
                  value={editedPost.seo_title || ''}
                  onChange={(e) => setEditedPost({ ...editedPost, seo_title: e.target.value })}
                  placeholder="SEO优化的标题（建议50-60字符）"
                />
                <p className="text-xs text-gray-500">
                  当前长度: {(editedPost.seo_title || '').length} 字符
                </p>
              </div>

              {/* SEO描述 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">SEO描述</label>
                <Textarea
                  value={editedPost.seo_description || ''}
                  onChange={(e) => setEditedPost({ ...editedPost, seo_description: e.target.value })}
                  placeholder="SEO描述（建议150-160字符）"
                  rows={3}
                />
                <p className="text-xs text-gray-500">
                  当前长度: {(editedPost.seo_description || '').length} 字符
                </p>
              </div>

              {/* SEO关键词 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">SEO关键词（用逗号分隔）</label>
                <Input
                  value={(editedPost.seo_keywords || []).join(', ')}
                  onChange={(e) => handleKeywordsChange(e.target.value)}
                  placeholder="例如：人工智能, 机器学习, 深度学习"
                />
                <div className="flex flex-wrap gap-1">
                  {(editedPost.seo_keywords || []).map((keyword, index) => (
                    <Badge key={index} variant="secondary">
                      {keyword}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
