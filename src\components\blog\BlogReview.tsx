'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Eye, Edit, Trash2, CheckCircle, Clock, Archive } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import type { BlogPost, Author } from '@/types/blog';

export default function BlogReview() {
  const [blogs, setBlogs] = useState<any[]>([]);
  const [authors, setAuthors] = useState<Author[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedBlog, setSelectedBlog] = useState<any>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    author_id: 'all',
    language: 'all'
  });

  // 加载博文列表
  const loadBlogs = async () => {
    try {
      const params = new URLSearchParams();
      if (filters.status !== 'all') params.append('status', filters.status);
      if (filters.author_id !== 'all') params.append('author_id', filters.author_id);
      if (filters.language !== 'all') params.append('language', filters.language);
      
      const response = await fetch(`/api/blog?${params.toString()}`);
      const result = await response.json();
      
      if (result.success) {
        setBlogs(result.data);
      }
    } catch (error) {
      console.error('Failed to load blogs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载作者列表
  const loadAuthors = async () => {
    try {
      const response = await fetch('/api/authors');
      const result = await response.json();
      
      if (result.success) {
        setAuthors(result.data);
      }
    } catch (error) {
      console.error('Failed to load authors:', error);
    }
  };

  useEffect(() => {
    loadBlogs();
    loadAuthors();
  }, [filters]);

  // 发布博文
  const handlePublish = async (blogId: string) => {
    if (!confirm('确定要发布这篇文章吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/blog/${blogId}/publish`, {
        method: 'POST',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Publish failed');
      }

      // 重新加载博文列表
      await loadBlogs();
      alert('文章发布成功！');
      
    } catch (error) {
      console.error('Publish error:', error);
      alert(`发布失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 删除博文
  const handleDelete = async (blogId: string, title: string) => {
    if (!confirm(`确定要删除文章"${title}"吗？此操作不可恢复。`)) {
      return;
    }

    try {
      const response = await fetch(`/api/blog/${blogId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Delete failed');
      }

      // 重新加载博文列表
      await loadBlogs();
      alert('文章删除成功！');
      
    } catch (error) {
      console.error('Delete error:', error);
      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 预览博文
  const handlePreview = (blog: any) => {
    setSelectedBlog(blog);
    setIsPreviewOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />已发布</Badge>;
      case 'draft':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />草稿</Badge>;
      case 'archived':
        return <Badge variant="outline"><Archive className="w-3 h-3 mr-1" />已归档</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-gray-500">加载中...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">博文审核管理</h1>
      </div>

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">筛选条件</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">状态</label>
              <Select
                value={filters.status}
                onValueChange={(value) => setFilters({ ...filters, status: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="draft">草稿</SelectItem>
                  <SelectItem value="published">已发布</SelectItem>
                  <SelectItem value="archived">已归档</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">作者</label>
              <Select
                value={filters.author_id}
                onValueChange={(value) => setFilters({ ...filters, author_id: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部作者</SelectItem>
                  {authors.map((author) => (
                    <SelectItem key={author.id} value={author.id}>
                      {author.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">语言</label>
              <Select
                value={filters.language}
                onValueChange={(value) => setFilters({ ...filters, language: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部语言</SelectItem>
                  <SelectItem value="zh-CN">中文</SelectItem>
                  <SelectItem value="en-US">English</SelectItem>
                  <SelectItem value="ja-JP">日本語</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 博文列表 */}
      <div className="space-y-4">
        {blogs.map((blog) => (
          <Card key={blog.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg mb-2">{blog.title}</CardTitle>
                  <div className="flex items-center space-x-2 mb-2">
                    {getStatusBadge(blog.status)}
                    <Badge variant="outline">{blog.language}</Badge>
                    {blog.blog_series?.name && (
                      <Badge variant="secondary">系列: {blog.blog_series.name}</Badge>
                    )}
                  </div>
                  <CardDescription>
                    {blog.summary || '暂无摘要'}
                  </CardDescription>
                </div>
                
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handlePreview(blog)}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    预览
                  </Button>
                  
                  {blog.status === 'draft' && (
                    <Button
                      size="sm"
                      onClick={() => handlePublish(blog.id)}
                    >
                      <CheckCircle className="w-4 h-4 mr-1" />
                      发布
                    </Button>
                  )}
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDelete(blog.id, blog.title)}
                  >
                    <Trash2 className="w-4 h-4 mr-1" />
                    删除
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <div className="flex items-center space-x-4">
                  <span>作者: {blog.authors?.name}</span>
                  <span>创建: {new Date(blog.created_at).toLocaleDateString('zh-CN')}</span>
                  {blog.published_at && (
                    <span>发布: {new Date(blog.published_at).toLocaleDateString('zh-CN')}</span>
                  )}
                </div>
                
                {blog.tags && blog.tags.length > 0 && (
                  <div className="flex space-x-1">
                    {blog.tags.slice(0, 3).map((tag: string, index: number) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {blog.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{blog.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {blogs.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <p className="text-gray-500">没有找到符合条件的博文</p>
          </CardContent>
        </Card>
      )}

      {/* 预览对话框 */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{selectedBlog?.title}</DialogTitle>
            <DialogDescription>
              <div className="flex items-center space-x-2 mt-2">
                {selectedBlog && getStatusBadge(selectedBlog.status)}
                <Badge variant="outline">{selectedBlog?.language}</Badge>
                <span className="text-sm text-gray-500">
                  作者: {selectedBlog?.authors?.name}
                </span>
              </div>
            </DialogDescription>
          </DialogHeader>
          
          {selectedBlog && (
            <div className="space-y-4">
              {selectedBlog.summary && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">摘要</h4>
                  <p className="text-sm">{selectedBlog.summary}</p>
                </div>
              )}
              
              <div className="prose prose-sm max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {selectedBlog.content}
                </ReactMarkdown>
              </div>
              
              {selectedBlog.seo_title && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">SEO信息</h4>
                  <div className="space-y-2 text-sm">
                    <p><strong>SEO标题:</strong> {selectedBlog.seo_title}</p>
                    {selectedBlog.seo_description && (
                      <p><strong>SEO描述:</strong> {selectedBlog.seo_description}</p>
                    )}
                    {selectedBlog.seo_keywords && selectedBlog.seo_keywords.length > 0 && (
                      <div>
                        <strong>关键词:</strong>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {selectedBlog.seo_keywords.map((keyword: string, index: number) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {keyword}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
