module.exports = {

"[project]/.next-internal/server/app/api/blog/generate/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/ai/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AI_CONFIG": ()=>AI_CONFIG,
    "generateBlogPost": ()=>generateBlogPost,
    "generateSEOInfo": ()=>generateSEOInfo,
    "generateSeriesSummary": ()=>generateSeriesSummary,
    "openai": ()=>openai
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
if (!process.env.OPENAI_API_KEY) {
    throw new Error('Missing OpenAI API key');
}
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
const AI_CONFIG = {
    model: 'gpt-4o-mini',
    temperature: 0.7,
    max_tokens: 4000,
    top_p: 1,
    frequency_penalty: 0,
    presence_penalty: 0
};
async function generateBlogPost(prompt, config = AI_CONFIG) {
    try {
        const response = await openai.chat.completions.create({
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: `你是一个专业的博客写手，擅长创作高质量、SEO友好的博文内容。请严格按照用户要求的格式返回结果。`
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: config.temperature,
            max_tokens: config.max_tokens,
            top_p: config.top_p,
            frequency_penalty: config.frequency_penalty,
            presence_penalty: config.presence_penalty
        });
        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new Error('No content generated');
        }
        return content;
    } catch (error) {
        console.error('OpenAI API error:', error);
        throw new Error('Failed to generate content');
    }
}
async function generateSEOInfo(prompt, config = AI_CONFIG) {
    try {
        const response = await openai.chat.completions.create({
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: `你是一个SEO专家，擅长为博文生成优化的SEO信息。请严格按照JSON格式返回结果。`
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.3,
            max_tokens: 1000,
            top_p: config.top_p,
            frequency_penalty: config.frequency_penalty,
            presence_penalty: config.presence_penalty
        });
        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new Error('No SEO content generated');
        }
        return content;
    } catch (error) {
        console.error('OpenAI API error:', error);
        throw new Error('Failed to generate SEO info');
    }
}
async function generateSeriesSummary(prompt, config = AI_CONFIG) {
    try {
        const response = await openai.chat.completions.create({
            model: config.model,
            messages: [
                {
                    role: 'system',
                    content: `你是一个内容总结专家，擅长为博文系列生成简洁而全面的总结。`
                },
                {
                    role: 'user',
                    content: prompt
                }
            ],
            temperature: 0.5,
            max_tokens: 500,
            top_p: config.top_p,
            frequency_penalty: config.frequency_penalty,
            presence_penalty: config.presence_penalty
        });
        const content = response.choices[0]?.message?.content;
        if (!content) {
            throw new Error('No summary generated');
        }
        return content;
    } catch (error) {
        console.error('OpenAI API error:', error);
        throw new Error('Failed to generate series summary');
    }
}
}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/client.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "createServerClient": ()=>createServerClient,
    "supabase": ()=>supabase
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://ohcnehqjrqzjlgbmjcck.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9oY25laHFqcnF6amxnYm1qY2NrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyNjIzMzYsImV4cCI6MjA2OTgzODMzNn0.sKrSvG0qQEmQqgnoqSv-5EmvWRQ4FQAvSN2Yi_MMo7o");
// 检查环境变量是否配置
const isConfigured = supabaseUrl && supabaseAnonKey && supabaseUrl !== 'your_supabase_url_here' && supabaseAnonKey !== 'your_supabase_anon_key_here';
const supabase = ("TURBOPACK compile-time truthy", 1) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey) : "TURBOPACK unreachable";
const createServerClient = ()=>{
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!isConfigured || !supabaseServiceKey || supabaseServiceKey === 'your_supabase_service_role_key_here') {
        return null;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey);
};
}),
"[project]/src/lib/supabase/services.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "authorService": ()=>authorService,
    "blogService": ()=>blogService,
    "promptService": ()=>promptService,
    "seriesService": ()=>seriesService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/client.ts [app-route] (ecmascript)");
;
// 检查Supabase是否已配置
const checkSupabaseConfig = ()=>{
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"]) {
        throw new Error('Supabase未配置。请在.env.local文件中设置正确的Supabase环境变量。');
    }
};
const authorService = {
    async getAll () {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data;
    },
    async getById (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (author) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').insert(author).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        checkSupabaseConfig();
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('authors').delete().eq('id', id);
        if (error) throw error;
    }
};
const seriesService = {
    async getAll (authorId) {
        checkSupabaseConfig();
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').select('*').order('created_at', {
            ascending: false
        });
        if (authorId) {
            query = query.eq('author_id', authorId);
        }
        const { data, error } = await query;
        if (error) throw error;
        return data;
    },
    async getById (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (series) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').insert(series).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        checkSupabaseConfig();
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_series').delete().eq('id', id);
        if (error) throw error;
    },
    async getPostsInSeries (seriesId) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').select('*').eq('series_id', seriesId).order('series_order', {
            ascending: true
        });
        if (error) throw error;
        return data;
    }
};
const blogService = {
    async getAll (filters) {
        checkSupabaseConfig();
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').select(`
        *,
        authors(name, avatar_url),
        blog_series(name)
      `).order('created_at', {
            ascending: false
        });
        if (filters?.authorId) query = query.eq('author_id', filters.authorId);
        if (filters?.seriesId) query = query.eq('series_id', filters.seriesId);
        if (filters?.status) query = query.eq('status', filters.status);
        if (filters?.language) query = query.eq('language', filters.language);
        const { data, error } = await query;
        if (error) throw error;
        return data;
    },
    async getById (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').select(`
        *,
        authors(name, avatar_url),
        blog_series(name)
      `).eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (post) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').insert(post).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        checkSupabaseConfig();
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').delete().eq('id', id);
        if (error) throw error;
    },
    async publish (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('blog_posts').update({
            status: 'published',
            published_at: new Date().toISOString()
        }).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
};
const promptService = {
    async getAll (type) {
        checkSupabaseConfig();
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').select('*').order('created_at', {
            ascending: false
        });
        if (type) {
            query = query.eq('type', type);
        }
        const { data, error } = await query;
        if (error) throw error;
        return data;
    },
    async getById (id) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').select('*').eq('id', id).single();
        if (error) throw error;
        return data;
    },
    async create (template) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').insert(template).select().single();
        if (error) throw error;
        return data;
    },
    async update (id, updates) {
        checkSupabaseConfig();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    },
    async delete (id) {
        checkSupabaseConfig();
        const { error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].from('prompt_templates').delete().eq('id', id);
        if (error) throw error;
    }
};
}),
"[project]/src/lib/ai/blog-generator.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "generateBlog": ()=>generateBlog,
    "generateSEOForBlog": ()=>generateSEOForBlog,
    "updateSeriesSummary": ()=>updateSeriesSummary
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/services.ts [app-route] (ecmascript)");
;
;
// 替换模板变量的辅助函数
function replaceTemplateVariables(template, variables) {
    let result = template;
    for (const [key, value] of Object.entries(variables)){
        result = result.replace(new RegExp(`{${key}}`, 'g'), value);
    }
    return result;
}
// 解析JSON响应的辅助函数
function parseJSONResponse(content) {
    try {
        // 尝试直接解析
        return JSON.parse(content);
    } catch  {
        // 如果失败，尝试提取JSON部分
        const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/{[\s\S]*}/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[1] || jsonMatch[0]);
            } catch  {
                throw new Error('Failed to parse JSON response');
            }
        }
        throw new Error('No valid JSON found in response');
    }
}
// 获取系列上下文信息
async function getSeriesContext(seriesId) {
    try {
        const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["seriesService"].getById(seriesId);
        const posts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["seriesService"].getPostsInSeries(seriesId);
        if (posts.length === 0) {
            return `这是系列"${series.name}"的第一篇文章。系列描述：${series.description}`;
        }
        const publishedPosts = posts.filter((post)=>post.status === 'published');
        const postSummaries = publishedPosts.map((post, index)=>`${index + 1}. ${post.title}: ${post.summary || '暂无摘要'}`).join('\n');
        return `
系列背景：${series.name} - ${series.description}
系列总结：${series.summary || '暂无总结'}
已发布文章：
${postSummaries}

请基于以上系列背景和已发布文章，生成与系列主题相关且有逻辑连续性的新文章。`;
    } catch (error) {
        console.error('Error getting series context:', error);
        return '';
    }
}
async function generateBlog(request) {
    try {
        // 获取生成模板
        const templates = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promptService"].getAll('generation');
        const template = templates[0]; // 使用第一个生成模板
        if (!template) {
            throw new Error('No generation template found');
        }
        // 准备模板变量
        const variables = {
            input_type: request.type === 'keyword' ? '关键词' : request.type === 'topic' ? '话题' : '标题',
            input_content: request.input,
            language: request.language,
            series_context: request.series_id ? await getSeriesContext(request.series_id) : '这是一篇独立的博文。'
        };
        // 如果有额外指令，添加到模板中
        let finalTemplate = template.template;
        if (request.additional_instructions) {
            finalTemplate += `\n\n额外要求：${request.additional_instructions}`;
        }
        // 替换模板变量
        const prompt = replaceTemplateVariables(finalTemplate, variables);
        // 生成博文内容
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBlogPost"])(prompt);
        const parsedResponse = parseJSONResponse(response);
        // 验证响应格式
        if (!parsedResponse.title || !parsedResponse.content) {
            throw new Error('Invalid response format: missing title or content');
        }
        // 生成SEO信息
        const seoResponse = await generateSEOForBlog({
            title: parsedResponse.title,
            content: parsedResponse.content,
            language: request.language
        });
        return {
            title: parsedResponse.title,
            content: parsedResponse.content,
            summary: parsedResponse.summary || '',
            seo_title: seoResponse.seo_title,
            seo_description: seoResponse.seo_description,
            seo_keywords: seoResponse.seo_keywords,
            tags: seoResponse.tags,
            category: seoResponse.category
        };
    } catch (error) {
        console.error('Blog generation error:', error);
        throw new Error(`Failed to generate blog: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
async function generateSEOForBlog(params) {
    try {
        // 获取SEO模板
        const templates = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promptService"].getAll('seo');
        const template = templates[0];
        if (!template) {
            throw new Error('No SEO template found');
        }
        // 准备模板变量
        const variables = {
            title: params.title,
            content: params.content.substring(0, 2000),
            language: params.language
        };
        // 替换模板变量
        const prompt = replaceTemplateVariables(template.template, variables);
        // 生成SEO信息
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateSEOInfo"])(prompt);
        const parsedResponse = parseJSONResponse(response);
        return {
            seo_title: parsedResponse.seo_title || params.title,
            seo_description: parsedResponse.seo_description || '',
            seo_keywords: Array.isArray(parsedResponse.seo_keywords) ? parsedResponse.seo_keywords : [],
            tags: Array.isArray(parsedResponse.tags) ? parsedResponse.tags : [],
            category: parsedResponse.category || '未分类'
        };
    } catch (error) {
        console.error('SEO generation error:', error);
        // 返回默认值而不是抛出错误
        return {
            seo_title: params.title,
            seo_description: '',
            seo_keywords: [],
            tags: [],
            category: '未分类'
        };
    }
}
async function updateSeriesSummary(seriesId) {
    try {
        const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["seriesService"].getById(seriesId);
        const posts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["seriesService"].getPostsInSeries(seriesId);
        const publishedPosts = posts.filter((post)=>post.status === 'published');
        if (publishedPosts.length === 0) {
            return; // 没有已发布的文章，不需要更新总结
        }
        // 获取总结模板
        const templates = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promptService"].getAll('summary');
        const template = templates[0];
        if (!template) {
            console.warn('No summary template found');
            return;
        }
        // 准备文章列表
        const postsList = publishedPosts.map((post, index)=>`${index + 1}. 《${post.title}》\n   摘要：${post.summary || '暂无摘要'}\n   主要内容：${post.content.substring(0, 200)}...`).join('\n\n');
        // 准备模板变量
        const variables = {
            series_name: series.name,
            series_description: series.description || '',
            published_posts: postsList
        };
        // 替换模板变量
        const prompt = replaceTemplateVariables(template.template, variables);
        // 生成总结
        const summary = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateSeriesSummary"])(prompt);
        // 更新系列总结
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["seriesService"].update(seriesId, {
            summary
        });
    } catch (error) {
        console.error('Series summary update error:', error);
    // 不抛出错误，因为这不是关键功能
    }
}
}),
"[project]/src/app/api/blog/generate/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2f$blog$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/ai/blog-generator.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/services.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const body = await request.json();
        // 验证请求数据
        if (!body.input || !body.language || !body.author_id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Missing required fields: input, language, author_id'
            }, {
                status: 400
            });
        }
        if (![
            'keyword',
            'topic',
            'title'
        ].includes(body.type)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid type. Must be keyword, topic, or title'
            }, {
                status: 400
            });
        }
        // 生成博文
        const generatedBlog = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2f$blog$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateBlog"])(body);
        // 创建博文记录
        const blogPost = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["blogService"].create({
            title: generatedBlog.title,
            content: generatedBlog.content,
            summary: generatedBlog.summary,
            language: body.language,
            seo_title: generatedBlog.seo_title,
            seo_description: generatedBlog.seo_description,
            seo_keywords: generatedBlog.seo_keywords,
            tags: generatedBlog.tags,
            category: generatedBlog.category,
            author_id: body.author_id,
            series_id: body.series_id || null,
            series_order: body.series_id ? await getNextSeriesOrder(body.series_id) : null,
            status: 'draft'
        });
        // 如果是系列文章，更新系列总结
        if (body.series_id) {
            // 异步更新，不阻塞响应
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$ai$2f$blog$2d$generator$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateSeriesSummary"])(body.series_id).catch((error)=>console.error('Failed to update series summary:', error));
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                blog_post: blogPost,
                generated_content: generatedBlog
            }
        });
    } catch (error) {
        console.error('Blog generation API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate blog',
            details: error instanceof Error ? error.message : 'Unknown error'
        }, {
            status: 500
        });
    }
}
// 获取系列中下一个文章的顺序号
async function getNextSeriesOrder(seriesId) {
    try {
        const posts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$services$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["blogService"].getAll({
            seriesId
        });
        const maxOrder = posts.reduce((max, post)=>Math.max(max, post.series_order || 0), 0);
        return maxOrder + 1;
    } catch (error) {
        console.error('Error getting next series order:', error);
        return 1;
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__ed78ef1a._.js.map