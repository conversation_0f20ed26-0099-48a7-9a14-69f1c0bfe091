import { supabase, createServerClient } from './client';
import type { Database } from './types';
import type { BlogPost, BlogSeries, Author, PromptTemplate } from '@/types/blog';

type Tables = Database['public']['Tables'];

// 检查Supabase是否已配置
const checkSupabaseConfig = () => {
  if (!supabase) {
    throw new Error('Supabase未配置。请在.env.local文件中设置正确的Supabase环境变量。');
  }
};

// 作者相关操作
export const authorService = {
  async getAll() {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('authors')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data as Author[];
  },

  async getById(id: string) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('authors')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data as Author;
  },

  async create(author: Tables['authors']['Insert']) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('authors')
      .insert(author)
      .select()
      .single();

    if (error) throw error;
    return data as Author;
  },

  async update(id: string, updates: Tables['authors']['Update']) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('authors')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as Author;
  },

  async delete(id: string) {
    checkSupabaseConfig();
    const { error } = await supabase!
      .from('authors')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
};

// 博文系列相关操作
export const seriesService = {
  async getAll(authorId?: string) {
    checkSupabaseConfig();
    let query = supabase!
      .from('blog_series')
      .select('*')
      .order('created_at', { ascending: false });

    if (authorId) {
      query = query.eq('author_id', authorId);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data as BlogSeries[];
  },

  async getById(id: string) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_series')
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data as BlogSeries;
  },

  async create(series: Tables['blog_series']['Insert']) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_series')
      .insert(series)
      .select()
      .single();

    if (error) throw error;
    return data as BlogSeries;
  },

  async update(id: string, updates: Tables['blog_series']['Update']) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_series')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as BlogSeries;
  },

  async delete(id: string) {
    checkSupabaseConfig();
    const { error } = await supabase!
      .from('blog_series')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async getPostsInSeries(seriesId: string) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_posts')
      .select('*')
      .eq('series_id', seriesId)
      .order('series_order', { ascending: true });

    if (error) throw error;
    return data as BlogPost[];
  }
};

// 博文相关操作
export const blogService = {
  async getAll(filters?: {
    authorId?: string;
    seriesId?: string;
    status?: 'draft' | 'published' | 'archived';
    language?: string;
  }) {
    checkSupabaseConfig();
    let query = supabase!
      .from('blog_posts')
      .select(`
        *,
        authors(name, avatar_url),
        blog_series(name)
      `)
      .order('created_at', { ascending: false });

    if (filters?.authorId) query = query.eq('author_id', filters.authorId);
    if (filters?.seriesId) query = query.eq('series_id', filters.seriesId);
    if (filters?.status) query = query.eq('status', filters.status);
    if (filters?.language) query = query.eq('language', filters.language);

    const { data, error } = await query;
    if (error) throw error;
    return data;
  },

  async getById(id: string) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_posts')
      .select(`
        *,
        authors(name, avatar_url),
        blog_series(name)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async create(post: Tables['blog_posts']['Insert']) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_posts')
      .insert(post)
      .select()
      .single();

    if (error) throw error;
    return data as BlogPost;
  },

  async update(id: string, updates: Tables['blog_posts']['Update']) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_posts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as BlogPost;
  },

  async delete(id: string) {
    checkSupabaseConfig();
    const { error } = await supabase!
      .from('blog_posts')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  async publish(id: string) {
    checkSupabaseConfig();
    const { data, error } = await supabase!
      .from('blog_posts')
      .update({
        status: 'published',
        published_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as BlogPost;
  }
};


