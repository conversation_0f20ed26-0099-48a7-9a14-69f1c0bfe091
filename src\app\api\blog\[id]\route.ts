import { NextRequest, NextResponse } from 'next/server';
import { blogService } from '@/lib/supabase/services';

// 获取单个博文
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const blogPost = await blogService.getById(params.id);
    
    return NextResponse.json({
      success: true,
      data: blogPost
    });
    
  } catch (error) {
    console.error('Blog fetch error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch blog post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 更新博文
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    const blogPost = await blogService.update(params.id, body);
    
    return NextResponse.json({
      success: true,
      data: blogPost
    });
    
  } catch (error) {
    console.error('Blog update error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to update blog post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// 删除博文
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await blogService.delete(params.id);
    
    return NextResponse.json({
      success: true,
      message: 'Blog post deleted successfully'
    });
    
  } catch (error) {
    console.error('Blog deletion error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to delete blog post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
