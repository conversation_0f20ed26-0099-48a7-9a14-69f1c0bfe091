{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 447, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/types/blog.ts"], "sourcesContent": ["// 博文相关类型定义\n\nexport interface BlogPost {\n  id: string;\n  title: string;\n  content: string;\n  summary: string;\n  language: string;\n  status: 'draft' | 'published' | 'archived';\n  seo_title?: string;\n  seo_description?: string;\n  seo_keywords?: string[];\n  tags?: string[];\n  category?: string;\n  author_id: string;\n  series_id?: string;\n  series_order?: number;\n  created_at: string;\n  updated_at: string;\n  published_at?: string;\n}\n\nexport interface BlogSeries {\n  id: string;\n  name: string;\n  description: string;\n  language: string;\n  author_id: string;\n  posts_count: number;\n  summary: string; // 系列历史总结\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface Author {\n  id: string;\n  name: string;\n  bio?: string;\n  avatar_url?: string;\n  email?: string;\n  website?: string;\n  social_links?: {\n    twitter?: string;\n    linkedin?: string;\n    github?: string;\n  };\n  created_at: string;\n  updated_at: string;\n}\n\nexport interface BlogGenerationRequest {\n  type: 'keyword' | 'topic' | 'title';\n  input: string;\n  language: string;\n  series_id?: string;\n  author_id: string;\n  additional_instructions?: string;\n}\n\nexport interface BlogGenerationResponse {\n  title: string;\n  content: string;\n  summary: string;\n  seo_title: string;\n  seo_description: string;\n  seo_keywords: string[];\n  tags: string[];\n  category: string;\n}\n\nexport interface PromptTemplate {\n  id: string;\n  name: string;\n  description: string;\n  template: string;\n  variables: string[];\n  type: 'generation' | 'seo' | 'summary';\n  created_at: string;\n  updated_at: string;\n}\n\nexport type Language = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR' | 'es-ES' | 'fr-FR' | 'de-DE';\n\nexport const SUPPORTED_LANGUAGES: { code: Language; name: string }[] = [\n  { code: 'zh-CN', name: '中文' },\n  { code: 'en-US', name: 'English' },\n  { code: 'ja-JP', name: '日本語' },\n  { code: 'ko-KR', name: '한국어' },\n  { code: 'es-ES', name: 'Español' },\n  { code: 'fr-FR', name: 'Français' },\n  { code: 'de-DE', name: 'Deutsch' },\n];\n"], "names": [], "mappings": "AAAA,WAAW;;;;AAmFJ,MAAM,sBAA0D;IACrE;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAU;IACjC;QAAE,MAAM;QAAS,MAAM;IAAM;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAM;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAU;IACjC;QAAE,MAAM;QAAS,MAAM;IAAW;IAClC;QAAE,MAAM;QAAS,MAAM;IAAU;CAClC", "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/blog/BlogGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { SUPPORTED_LANGUAGES } from '@/types/blog';\nimport type { BlogGenerationRequest, Language, BlogSeries } from '@/types/blog';\n\ninterface BlogGeneratorProps {\n  onGenerated?: (blogPost: any) => void;\n}\n\nexport default function BlogGenerator({ onGenerated }: BlogGeneratorProps) {\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [series, setSeries] = useState<BlogSeries[]>([]);\n  const [formData, setFormData] = useState<Partial<BlogGenerationRequest>>({\n    type: 'keyword',\n    language: 'zh-CN',\n    author_id: '00000000-0000-0000-0000-000000000001', // 默认作者ID\n  });\n\n  // 加载系列列表\n  const loadSeries = async () => {\n    try {\n      const response = await fetch(`/api/series?author_id=${formData.author_id}`);\n      const result = await response.json();\n\n      if (result.success) {\n        setSeries(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load series:', error);\n    }\n  };\n\n  useEffect(() => {\n    if (formData.author_id) {\n      loadSeries();\n    }\n  }, [formData.author_id]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.input) {\n      alert('请输入内容');\n      return;\n    }\n\n    setIsGenerating(true);\n    \n    try {\n      const response = await fetch('/api/blog/generate', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Generation failed');\n      }\n\n      if (onGenerated) {\n        onGenerated(result.data);\n      }\n\n      // 重置表单\n      setFormData({\n        ...formData,\n        input: '',\n        additional_instructions: '',\n      });\n\n      alert('博文生成成功！');\n    } catch (error) {\n      console.error('Generation error:', error);\n      alert(`生成失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle>AI博文生成器</CardTitle>\n        <CardDescription>\n          输入关键词、话题或标题，AI将为您生成高质量的博文内容\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* 生成类型选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">生成类型</label>\n            <Select\n              value={formData.type}\n              onValueChange={(value: 'keyword' | 'topic' | 'title') =>\n                setFormData({ ...formData, type: value })\n              }\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"keyword\">关键词</SelectItem>\n                <SelectItem value=\"topic\">话题</SelectItem>\n                <SelectItem value=\"title\">标题</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 输入内容 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">\n              {formData.type === 'keyword' && '关键词'}\n              {formData.type === 'topic' && '话题描述'}\n              {formData.type === 'title' && '文章标题'}\n            </label>\n            <Input\n              value={formData.input || ''}\n              onChange={(e) => setFormData({ ...formData, input: e.target.value })}\n              placeholder={\n                formData.type === 'keyword' ? '例如：人工智能, 机器学习' :\n                formData.type === 'topic' ? '例如：人工智能在医疗领域的应用' :\n                '例如：AI如何改变我们的生活'\n              }\n              required\n            />\n          </div>\n\n          {/* 语言选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">语言</label>\n            <Select\n              value={formData.language}\n              onValueChange={(value: Language) =>\n                setFormData({ ...formData, language: value })\n              }\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {SUPPORTED_LANGUAGES.map((lang) => (\n                  <SelectItem key={lang.code} value={lang.code}>\n                    {lang.name}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 系列选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">博文系列（可选）</label>\n            <Select\n              value={formData.series_id || 'none'}\n              onValueChange={(value) =>\n                setFormData({ ...formData, series_id: value === 'none' ? undefined : value })\n              }\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"选择系列或留空创建独立文章\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"none\">独立文章</SelectItem>\n                {series.map((seriesItem) => (\n                  <SelectItem key={seriesItem.id} value={seriesItem.id}>\n                    {seriesItem.name} ({seriesItem.posts_count} 篇文章)\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 额外指令 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">额外指令（可选）</label>\n            <Textarea\n              value={formData.additional_instructions || ''}\n              onChange={(e) =>\n                setFormData({ ...formData, additional_instructions: e.target.value })\n              }\n              placeholder=\"例如：请重点关注实际应用案例，文章风格要轻松易懂\"\n              rows={3}\n            />\n          </div>\n\n          {/* 提交按钮 */}\n          <Button\n            type=\"submit\"\n            disabled={isGenerating}\n            className=\"w-full\"\n          >\n            {isGenerating ? '正在生成...' : '生成博文'}\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAee,SAAS,cAAc,EAAE,WAAW,EAAsB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;QACvE,MAAM;QACN,UAAU;QACV,WAAW;IACb;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,SAAS,SAAS,EAAE;YAC1E,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,SAAS,EAAE;YACtB;QACF;IACF,GAAG;QAAC,SAAS,SAAS;KAAC;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,IAAI,aAAa;gBACf,YAAY,OAAO,IAAI;YACzB;YAEA,OAAO;YACP,YAAY;gBACV,GAAG,QAAQ;gBACX,OAAO;gBACP,yBAAyB;YAC3B;YAEA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;kCAAC;;;;;;kCACX,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,IAAI;oCACpB,eAAe,CAAC,QACd,YAAY;4CAAE,GAAG,QAAQ;4CAAE,MAAM;wCAAM;;sDAGzC,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;wCACd,SAAS,IAAI,KAAK,aAAa;wCAC/B,SAAS,IAAI,KAAK,WAAW;wCAC7B,SAAS,IAAI,KAAK,WAAW;;;;;;;8CAEhC,8OAAC,iIAAA,CAAA,QAAK;oCACJ,OAAO,SAAS,KAAK,IAAI;oCACzB,UAAU,CAAC,IAAM,YAAY;4CAAE,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAClE,aACE,SAAS,IAAI,KAAK,YAAY,kBAC9B,SAAS,IAAI,KAAK,UAAU,oBAC5B;oCAEF,QAAQ;;;;;;;;;;;;sCAKZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,QAAQ;oCACxB,eAAe,CAAC,QACd,YAAY;4CAAE,GAAG,QAAQ;4CAAE,UAAU;wCAAM;;sDAG7C,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,8OAAC,kIAAA,CAAA,gBAAa;sDACX,oHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC,kIAAA,CAAA,aAAU;oDAAiB,OAAO,KAAK,IAAI;8DACzC,KAAK,IAAI;mDADK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCASlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,kIAAA,CAAA,SAAM;oCACL,OAAO,SAAS,SAAS,IAAI;oCAC7B,eAAe,CAAC,QACd,YAAY;4CAAE,GAAG,QAAQ;4CAAE,WAAW,UAAU,SAAS,YAAY;wCAAM;;sDAG7E,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8DACZ,8OAAC,kIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;gDACxB,OAAO,GAAG,CAAC,CAAC,2BACX,8OAAC,kIAAA,CAAA,aAAU;wDAAqB,OAAO,WAAW,EAAE;;4DACjD,WAAW,IAAI;4DAAC;4DAAG,WAAW,WAAW;4DAAC;;uDAD5B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAStC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAAsB;;;;;;8CACvC,8OAAC,oIAAA,CAAA,WAAQ;oCACP,OAAO,SAAS,uBAAuB,IAAI;oCAC3C,UAAU,CAAC,IACT,YAAY;4CAAE,GAAG,QAAQ;4CAAE,yBAAyB,EAAE,MAAM,CAAC,KAAK;wCAAC;oCAErE,aAAY;oCACZ,MAAM;;;;;;;;;;;;sCAKV,8OAAC,kIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/blog/BlogReview.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Eye, Edit, Trash2, CheckCircle, Clock, Archive } from 'lucide-react';\nimport ReactMarkdown from 'react-markdown';\nimport remarkGfm from 'remark-gfm';\nimport type { BlogPost, Author } from '@/types/blog';\n\nexport default function BlogReview() {\n  const [blogs, setBlogs] = useState<any[]>([]);\n  const [authors, setAuthors] = useState<Author[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [selectedBlog, setSelectedBlog] = useState<any>(null);\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const [filters, setFilters] = useState({\n    status: 'all',\n    author_id: 'all',\n    language: 'all'\n  });\n\n  // 加载博文列表\n  const loadBlogs = async () => {\n    try {\n      const params = new URLSearchParams();\n      if (filters.status !== 'all') params.append('status', filters.status);\n      if (filters.author_id !== 'all') params.append('author_id', filters.author_id);\n      if (filters.language !== 'all') params.append('language', filters.language);\n      \n      const response = await fetch(`/api/blog?${params.toString()}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setBlogs(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load blogs:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // 加载作者列表\n  const loadAuthors = async () => {\n    try {\n      const response = await fetch('/api/authors');\n      const result = await response.json();\n      \n      if (result.success) {\n        setAuthors(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load authors:', error);\n    }\n  };\n\n  useEffect(() => {\n    loadBlogs();\n    loadAuthors();\n  }, [filters]);\n\n  // 发布博文\n  const handlePublish = async (blogId: string) => {\n    if (!confirm('确定要发布这篇文章吗？')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/blog/${blogId}/publish`, {\n        method: 'POST',\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Publish failed');\n      }\n\n      // 重新加载博文列表\n      await loadBlogs();\n      alert('文章发布成功！');\n      \n    } catch (error) {\n      console.error('Publish error:', error);\n      alert(`发布失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  // 删除博文\n  const handleDelete = async (blogId: string, title: string) => {\n    if (!confirm(`确定要删除文章\"${title}\"吗？此操作不可恢复。`)) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/blog/${blogId}`, {\n        method: 'DELETE',\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Delete failed');\n      }\n\n      // 重新加载博文列表\n      await loadBlogs();\n      alert('文章删除成功！');\n      \n    } catch (error) {\n      console.error('Delete error:', error);\n      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  // 预览博文\n  const handlePreview = (blog: any) => {\n    setSelectedBlog(blog);\n    setIsPreviewOpen(true);\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'published':\n        return <Badge className=\"bg-green-100 text-green-800\"><CheckCircle className=\"w-3 h-3 mr-1\" />已发布</Badge>;\n      case 'draft':\n        return <Badge variant=\"secondary\"><Clock className=\"w-3 h-3 mr-1\" />草稿</Badge>;\n      case 'archived':\n        return <Badge variant=\"outline\"><Archive className=\"w-3 h-3 mr-1\" />已归档</Badge>;\n      default:\n        return <Badge variant=\"secondary\">{status}</Badge>;\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <p className=\"text-center text-gray-500\">加载中...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold\">博文审核管理</h1>\n      </div>\n\n      {/* 筛选器 */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">筛选条件</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">状态</label>\n              <Select\n                value={filters.status}\n                onValueChange={(value) => setFilters({ ...filters, status: value })}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">全部状态</SelectItem>\n                  <SelectItem value=\"draft\">草稿</SelectItem>\n                  <SelectItem value=\"published\">已发布</SelectItem>\n                  <SelectItem value=\"archived\">已归档</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            \n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">作者</label>\n              <Select\n                value={filters.author_id}\n                onValueChange={(value) => setFilters({ ...filters, author_id: value })}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">全部作者</SelectItem>\n                  {authors.map((author) => (\n                    <SelectItem key={author.id} value={author.id}>\n                      {author.name}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n            \n            <div className=\"space-y-2\">\n              <label className=\"text-sm font-medium\">语言</label>\n              <Select\n                value={filters.language}\n                onValueChange={(value) => setFilters({ ...filters, language: value })}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"all\">全部语言</SelectItem>\n                  <SelectItem value=\"zh-CN\">中文</SelectItem>\n                  <SelectItem value=\"en-US\">English</SelectItem>\n                  <SelectItem value=\"ja-JP\">日本語</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* 博文列表 */}\n      <div className=\"space-y-4\">\n        {blogs.map((blog) => (\n          <Card key={blog.id} className=\"hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <CardTitle className=\"text-lg mb-2\">{blog.title}</CardTitle>\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    {getStatusBadge(blog.status)}\n                    <Badge variant=\"outline\">{blog.language}</Badge>\n                    {blog.blog_series?.name && (\n                      <Badge variant=\"secondary\">系列: {blog.blog_series.name}</Badge>\n                    )}\n                  </div>\n                  <CardDescription>\n                    {blog.summary || '暂无摘要'}\n                  </CardDescription>\n                </div>\n                \n                <div className=\"flex space-x-2\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={() => handlePreview(blog)}\n                  >\n                    <Eye className=\"w-4 h-4 mr-1\" />\n                    预览\n                  </Button>\n                  \n                  {blog.status === 'draft' && (\n                    <Button\n                      size=\"sm\"\n                      onClick={() => handlePublish(blog.id)}\n                    >\n                      <CheckCircle className=\"w-4 h-4 mr-1\" />\n                      发布\n                    </Button>\n                  )}\n                  \n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={() => handleDelete(blog.id, blog.title)}\n                  >\n                    <Trash2 className=\"w-4 h-4 mr-1\" />\n                    删除\n                  </Button>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                <div className=\"flex items-center space-x-4\">\n                  <span>作者: {blog.authors?.name}</span>\n                  <span>创建: {new Date(blog.created_at).toLocaleDateString('zh-CN')}</span>\n                  {blog.published_at && (\n                    <span>发布: {new Date(blog.published_at).toLocaleDateString('zh-CN')}</span>\n                  )}\n                </div>\n                \n                {blog.tags && blog.tags.length > 0 && (\n                  <div className=\"flex space-x-1\">\n                    {blog.tags.slice(0, 3).map((tag: string, index: number) => (\n                      <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                        {tag}\n                      </Badge>\n                    ))}\n                    {blog.tags.length > 3 && (\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        +{blog.tags.length - 3}\n                      </Badge>\n                    )}\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {blogs.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <p className=\"text-gray-500\">没有找到符合条件的博文</p>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* 预览对话框 */}\n      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>\n        <DialogContent className=\"max-w-4xl max-h-[80vh] overflow-y-auto\">\n          <DialogHeader>\n            <DialogTitle>{selectedBlog?.title}</DialogTitle>\n            <DialogDescription>\n              <div className=\"flex items-center space-x-2 mt-2\">\n                {selectedBlog && getStatusBadge(selectedBlog.status)}\n                <Badge variant=\"outline\">{selectedBlog?.language}</Badge>\n                <span className=\"text-sm text-gray-500\">\n                  作者: {selectedBlog?.authors?.name}\n                </span>\n              </div>\n            </DialogDescription>\n          </DialogHeader>\n          \n          {selectedBlog && (\n            <div className=\"space-y-4\">\n              {selectedBlog.summary && (\n                <div className=\"bg-gray-50 p-4 rounded-lg\">\n                  <h4 className=\"font-semibold mb-2\">摘要</h4>\n                  <p className=\"text-sm\">{selectedBlog.summary}</p>\n                </div>\n              )}\n              \n              <div className=\"prose prose-sm max-w-none\">\n                <ReactMarkdown remarkPlugins={[remarkGfm]}>\n                  {selectedBlog.content}\n                </ReactMarkdown>\n              </div>\n              \n              {selectedBlog.seo_title && (\n                <div className=\"bg-blue-50 p-4 rounded-lg\">\n                  <h4 className=\"font-semibold mb-2\">SEO信息</h4>\n                  <div className=\"space-y-2 text-sm\">\n                    <p><strong>SEO标题:</strong> {selectedBlog.seo_title}</p>\n                    {selectedBlog.seo_description && (\n                      <p><strong>SEO描述:</strong> {selectedBlog.seo_description}</p>\n                    )}\n                    {selectedBlog.seo_keywords && selectedBlog.seo_keywords.length > 0 && (\n                      <div>\n                        <strong>关键词:</strong>\n                        <div className=\"flex flex-wrap gap-1 mt-1\">\n                          {selectedBlog.seo_keywords.map((keyword: string, index: number) => (\n                            <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                              {keyword}\n                            </Badge>\n                          ))}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAXA;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,QAAQ;QACR,WAAW;QACX,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,MAAM,KAAK,OAAO,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YACpE,IAAI,QAAQ,SAAS,KAAK,OAAO,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YAC7E,IAAI,QAAQ,QAAQ,KAAK,OAAO,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YAE1E,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,QAAQ,IAAI;YAC7D,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,IAAI;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,aAAa;QACf;IACF;IAEA,SAAS;IACT,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC;KAAQ;IAEZ,OAAO;IACP,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,QAAQ,gBAAgB;YAC3B;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,OAAO,QAAQ,CAAC,EAAE;gBAC1D,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YACN,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO,QAAgB;QAC1C,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,WAAW,CAAC,GAAG;YAC3C;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE;gBAClD,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YACN,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAA8B,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAChG,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCAAY,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YACtE,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;;sCAAU,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YACtE;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;;QACvC;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;;;;;IAIjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAqB;;;;;;;;;;;0BAIrC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,QAAQ,MAAM;4CACrB,eAAe,CAAC,QAAU,WAAW;oDAAE,GAAG,OAAO;oDAAE,QAAQ;gDAAM;;8DAEjE,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAY;;;;;;sEAC9B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAW;;;;;;;;;;;;;;;;;;;;;;;;8CAKnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,QAAQ,SAAS;4CACxB,eAAe,CAAC,QAAU,WAAW;oDAAE,GAAG,OAAO;oDAAE,WAAW;gDAAM;;8DAEpE,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;wDACvB,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAiB,OAAO,OAAO,EAAE;0EACzC,OAAO,IAAI;+DADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAQlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAAsB;;;;;;sDACvC,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,QAAQ,QAAQ;4CACvB,eAAe,CAAC,QAAU,WAAW;oDAAE,GAAG,OAAO;oDAAE,UAAU;gDAAM;;8DAEnE,8OAAC,kIAAA,CAAA,gBAAa;8DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8DAEd,8OAAC,kIAAA,CAAA,gBAAa;;sEACZ,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAM;;;;;;sEACxB,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;sEAC1B,8OAAC,kIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,gIAAA,CAAA,OAAI;wBAAe,WAAU;;0CAC5B,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAgB,KAAK,KAAK;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;;wDACZ,eAAe,KAAK,MAAM;sEAC3B,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW,KAAK,QAAQ;;;;;;wDACtC,KAAK,WAAW,EAAE,sBACjB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;;gEAAY;gEAAK,KAAK,WAAW,CAAC,IAAI;;;;;;;;;;;;;8DAGzD,8OAAC,gIAAA,CAAA,kBAAe;8DACb,KAAK,OAAO,IAAI;;;;;;;;;;;;sDAIrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,cAAc;;sEAE7B,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAIjC,KAAK,MAAM,KAAK,yBACf,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS,IAAM,cAAc,KAAK,EAAE;;sEAEpC,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAK5C,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,aAAa,KAAK,EAAE,EAAE,KAAK,KAAK;;sEAE/C,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;wDAAK;wDAAK,KAAK,OAAO,EAAE;;;;;;;8DACzB,8OAAC;;wDAAK;wDAAK,IAAI,KAAK,KAAK,UAAU,EAAE,kBAAkB,CAAC;;;;;;;gDACvD,KAAK,YAAY,kBAChB,8OAAC;;wDAAK;wDAAK,IAAI,KAAK,KAAK,YAAY,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;wCAI7D,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAa,sBACvC,8OAAC,iIAAA,CAAA,QAAK;wDAAa,SAAQ;wDAAU,WAAU;kEAC5C;uDADS;;;;;gDAIb,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;wDAAU;wDACzC,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;uBAnExB,KAAK,EAAE;;;;;;;;;;YA8ErB,MAAM,MAAM,KAAK,mBAChB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;0BAMnC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAe,cAAc;0BACzC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAE,cAAc;;;;;;8CAC5B,8OAAC,kIAAA,CAAA,oBAAiB;8CAChB,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,gBAAgB,eAAe,aAAa,MAAM;0DACnD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAW,cAAc;;;;;;0DACxC,8OAAC;gDAAK,WAAU;;oDAAwB;oDACjC,cAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;;wBAMnC,8BACC,8OAAC;4BAAI,WAAU;;gCACZ,aAAa,OAAO,kBACnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAW,aAAa,OAAO;;;;;;;;;;;;8CAIhD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wLAAA,CAAA,UAAa;wCAAC,eAAe;4CAAC,6IAAA,CAAA,UAAS;yCAAC;kDACtC,aAAa,OAAO;;;;;;;;;;;gCAIxB,aAAa,SAAS,kBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;wDAAE,aAAa,SAAS;;;;;;;gDACjD,aAAa,eAAe,kBAC3B,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;wDAAE,aAAa,eAAe;;;;;;;gDAEzD,aAAa,YAAY,IAAI,aAAa,YAAY,CAAC,MAAM,GAAG,mBAC/D,8OAAC;;sEACC,8OAAC;sEAAO;;;;;;sEACR,8OAAC;4DAAI,WAAU;sEACZ,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,SAAiB,sBAC/C,8OAAC,iIAAA,CAAA,QAAK;oEAAa,SAAQ;oEAAY,WAAU;8EAC9C;mEADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBxC", "debugId": null}}, {"offset": {"line": 2150, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/series/SeriesManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { SUPPORTED_LANGUAGES } from '@/types/blog';\nimport type { BlogSeries, Language } from '@/types/blog';\n\ninterface SeriesManagerProps {\n  authorId: string;\n  onSeriesSelected?: (series: BlogSeries) => void;\n}\n\nexport default function SeriesManager({ authorId, onSeriesSelected }: SeriesManagerProps) {\n  const [series, setSeries] = useState<BlogSeries[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isCreating, setIsCreating] = useState(false);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [newSeries, setNewSeries] = useState({\n    name: '',\n    description: '',\n    language: 'zh-CN' as Language,\n  });\n\n  // 加载系列列表\n  const loadSeries = async () => {\n    try {\n      const response = await fetch(`/api/series?author_id=${authorId}`);\n      const result = await response.json();\n      \n      if (result.success) {\n        setSeries(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load series:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadSeries();\n  }, [authorId]);\n\n  // 创建新系列\n  const handleCreateSeries = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!newSeries.name) {\n      alert('请输入系列名称');\n      return;\n    }\n\n    setIsCreating(true);\n    \n    try {\n      const response = await fetch('/api/series', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...newSeries,\n          author_id: authorId,\n        }),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Creation failed');\n      }\n\n      // 重新加载系列列表\n      await loadSeries();\n      \n      // 重置表单\n      setNewSeries({\n        name: '',\n        description: '',\n        language: 'zh-CN',\n      });\n      \n      setIsDialogOpen(false);\n      alert('系列创建成功！');\n      \n    } catch (error) {\n      console.error('Series creation error:', error);\n      alert(`创建失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <p className=\"text-center text-gray-500\">加载中...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold\">博文系列管理</h2>\n        \n        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>\n          <DialogTrigger asChild>\n            <Button>创建新系列</Button>\n          </DialogTrigger>\n          <DialogContent>\n            <DialogHeader>\n              <DialogTitle>创建新系列</DialogTitle>\n              <DialogDescription>\n                创建一个新的博文系列来组织相关的文章\n              </DialogDescription>\n            </DialogHeader>\n            \n            <form onSubmit={handleCreateSeries} className=\"space-y-4\">\n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">系列名称</label>\n                <Input\n                  value={newSeries.name}\n                  onChange={(e) => setNewSeries({ ...newSeries, name: e.target.value })}\n                  placeholder=\"例如：AI技术入门系列\"\n                  required\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">系列描述</label>\n                <Textarea\n                  value={newSeries.description}\n                  onChange={(e) => setNewSeries({ ...newSeries, description: e.target.value })}\n                  placeholder=\"描述这个系列的主题和目标\"\n                  rows={3}\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">语言</label>\n                <Select\n                  value={newSeries.language}\n                  onValueChange={(value: Language) =>\n                    setNewSeries({ ...newSeries, language: value })\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {SUPPORTED_LANGUAGES.map((lang) => (\n                      <SelectItem key={lang.code} value={lang.code}>\n                        {lang.name}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n              </div>\n              \n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsDialogOpen(false)}\n                >\n                  取消\n                </Button>\n                <Button type=\"submit\" disabled={isCreating}>\n                  {isCreating ? '创建中...' : '创建'}\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* 系列列表 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {series.map((seriesItem) => (\n          <Card \n            key={seriesItem.id} \n            className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n            onClick={() => onSeriesSelected?.(seriesItem)}\n          >\n            <CardHeader>\n              <CardTitle className=\"text-lg\">{seriesItem.name}</CardTitle>\n              <CardDescription>\n                {seriesItem.description || '暂无描述'}\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex justify-between items-center text-sm text-gray-500\">\n                <span>{seriesItem.posts_count} 篇文章</span>\n                <Badge variant=\"outline\">\n                  {SUPPORTED_LANGUAGES.find(lang => lang.code === seriesItem.language)?.name}\n                </Badge>\n              </div>\n              {seriesItem.summary && (\n                <p className=\"text-sm text-gray-600 mt-2 line-clamp-2\">\n                  {seriesItem.summary}\n                </p>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {series.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <p className=\"text-gray-500 mb-4\">还没有创建任何系列</p>\n            <Button onClick={() => setIsDialogOpen(true)}>\n              创建第一个系列\n            </Button>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAkBe,SAAS,cAAc,EAAE,QAAQ,EAAE,gBAAgB,EAAsB;IACtF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,aAAa;QACb,UAAU;IACZ;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,UAAU;YAChE,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,UAAU,OAAO,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,QAAQ;IACR,MAAM,qBAAqB,OAAO;QAChC,EAAE,cAAc;QAEhB,IAAI,CAAC,UAAU,IAAI,EAAE;YACnB,MAAM;YACN;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,SAAS;oBACZ,WAAW;gBACb;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YAEN,OAAO;YACP,aAAa;gBACX,MAAM;gBACN,aAAa;gBACb,UAAU;YACZ;YAEA,gBAAgB;YAChB,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;;;;;IAIjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCAEnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc;;0CACxC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;0CAEV,8OAAC,kIAAA,CAAA,gBAAa;;kDACZ,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAKrB,8OAAC;wCAAK,UAAU;wCAAoB,WAAU;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,UAAU,IAAI;wDACrB,UAAU,CAAC,IAAM,aAAa;gEAAE,GAAG,SAAS;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACnE,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,UAAU,WAAW;wDAC5B,UAAU,CAAC,IAAM,aAAa;gEAAE,GAAG,SAAS;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC1E,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO,UAAU,QAAQ;wDACzB,eAAe,CAAC,QACd,aAAa;gEAAE,GAAG,SAAS;gEAAE,UAAU;4DAAM;;0EAG/C,8OAAC,kIAAA,CAAA,gBAAa;0EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0EAEd,8OAAC,kIAAA,CAAA,gBAAa;0EACX,oHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC,kIAAA,CAAA,aAAU;wEAAiB,OAAO,KAAK,IAAI;kFACzC,KAAK,IAAI;uEADK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0DAQlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,UAAU;kEAC7B,aAAa,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,2BACX,8OAAC,gIAAA,CAAA,OAAI;wBAEH,WAAU;wBACV,SAAS,IAAM,mBAAmB;;0CAElC,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW,WAAW,IAAI;;;;;;kDAC/C,8OAAC,gIAAA,CAAA,kBAAe;kDACb,WAAW,WAAW,IAAI;;;;;;;;;;;;0CAG/B,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAM,WAAW,WAAW;oDAAC;;;;;;;0DAC9B,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,oHAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,QAAQ,GAAG;;;;;;;;;;;;oCAGzE,WAAW,OAAO,kBACjB,8OAAC;wCAAE,WAAU;kDACV,WAAW,OAAO;;;;;;;;;;;;;uBAnBpB,WAAW,EAAE;;;;;;;;;;YA2BvB,OAAO,MAAM,KAAK,mBACjB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,gBAAgB;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAQ1D", "debugId": null}}, {"offset": {"line": 2618, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2668, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/author/AuthorManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport { User, Mail, Globe, Edit, Trash2 } from 'lucide-react';\nimport type { Author } from '@/types/blog';\n\ninterface AuthorManagerProps {\n  onAuthorSelected?: (author: Author) => void;\n}\n\nexport default function AuthorManager({ onAuthorSelected }: AuthorManagerProps) {\n  const [authors, setAuthors] = useState<Author[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isCreating, setIsCreating] = useState(false);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [editingAuthor, setEditingAuthor] = useState<Author | null>(null);\n  const [formData, setFormData] = useState({\n    name: '',\n    bio: '',\n    email: '',\n    website: '',\n    avatar_url: '',\n    social_links: {\n      twitter: '',\n      linkedin: '',\n      github: ''\n    }\n  });\n\n  // 加载作者列表\n  const loadAuthors = async () => {\n    try {\n      const response = await fetch('/api/authors');\n      const result = await response.json();\n      \n      if (result.success) {\n        setAuthors(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load authors:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadAuthors();\n  }, []);\n\n  // 重置表单\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      bio: '',\n      email: '',\n      website: '',\n      avatar_url: '',\n      social_links: {\n        twitter: '',\n        linkedin: '',\n        github: ''\n      }\n    });\n    setEditingAuthor(null);\n  };\n\n  // 编辑作者\n  const handleEdit = (author: Author) => {\n    setEditingAuthor(author);\n    setFormData({\n      name: author.name,\n      bio: author.bio || '',\n      email: author.email || '',\n      website: author.website || '',\n      avatar_url: author.avatar_url || '',\n      social_links: {\n        twitter: author.social_links?.twitter || '',\n        linkedin: author.social_links?.linkedin || '',\n        github: author.social_links?.github || ''\n      }\n    });\n    setIsDialogOpen(true);\n  };\n\n  // 创建或更新作者\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.name) {\n      alert('请输入作者姓名');\n      return;\n    }\n\n    setIsCreating(true);\n    \n    try {\n      const url = editingAuthor ? `/api/authors/${editingAuthor.id}` : '/api/authors';\n      const method = editingAuthor ? 'PUT' : 'POST';\n      \n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Operation failed');\n      }\n\n      // 重新加载作者列表\n      await loadAuthors();\n      \n      // 重置表单\n      resetForm();\n      setIsDialogOpen(false);\n      \n      alert(editingAuthor ? '作者更新成功！' : '作者创建成功！');\n      \n    } catch (error) {\n      console.error('Author operation error:', error);\n      alert(`操作失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsCreating(false);\n    }\n  };\n\n  // 删除作者\n  const handleDelete = async (author: Author) => {\n    if (!confirm(`确定要删除作者\"${author.name}\"吗？这将同时删除该作者的所有文章。`)) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/authors/${author.id}`, {\n        method: 'DELETE',\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Delete failed');\n      }\n\n      // 重新加载作者列表\n      await loadAuthors();\n      alert('作者删除成功！');\n      \n    } catch (error) {\n      console.error('Author deletion error:', error);\n      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <p className=\"text-center text-gray-500\">加载中...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold\">作者管理</h2>\n        \n        <Dialog open={isDialogOpen} onOpenChange={(open) => {\n          setIsDialogOpen(open);\n          if (!open) resetForm();\n        }}>\n          <DialogTrigger asChild>\n            <Button>添加作者</Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl\">\n            <DialogHeader>\n              <DialogTitle>\n                {editingAuthor ? '编辑作者' : '添加新作者'}\n              </DialogTitle>\n              <DialogDescription>\n                {editingAuthor ? '修改作者信息' : '创建一个新的作者档案'}\n              </DialogDescription>\n            </DialogHeader>\n            \n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">姓名 *</label>\n                  <Input\n                    value={formData.name}\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                    placeholder=\"作者姓名\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">邮箱</label>\n                  <Input\n                    type=\"email\"\n                    value={formData.email}\n                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">个人简介</label>\n                <Textarea\n                  value={formData.bio}\n                  onChange={(e) => setFormData({ ...formData, bio: e.target.value })}\n                  placeholder=\"简单介绍一下作者\"\n                  rows={3}\n                />\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">网站</label>\n                  <Input\n                    value={formData.website}\n                    onChange={(e) => setFormData({ ...formData, website: e.target.value })}\n                    placeholder=\"https://example.com\"\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">头像URL</label>\n                  <Input\n                    value={formData.avatar_url}\n                    onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}\n                    placeholder=\"https://example.com/avatar.jpg\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">社交媒体</label>\n                <div className=\"grid grid-cols-3 gap-2\">\n                  <Input\n                    value={formData.social_links.twitter}\n                    onChange={(e) => setFormData({\n                      ...formData,\n                      social_links: { ...formData.social_links, twitter: e.target.value }\n                    })}\n                    placeholder=\"Twitter用户名\"\n                  />\n                  <Input\n                    value={formData.social_links.linkedin}\n                    onChange={(e) => setFormData({\n                      ...formData,\n                      social_links: { ...formData.social_links, linkedin: e.target.value }\n                    })}\n                    placeholder=\"LinkedIn用户名\"\n                  />\n                  <Input\n                    value={formData.social_links.github}\n                    onChange={(e) => setFormData({\n                      ...formData,\n                      social_links: { ...formData.social_links, github: e.target.value }\n                    })}\n                    placeholder=\"GitHub用户名\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsDialogOpen(false)}\n                >\n                  取消\n                </Button>\n                <Button type=\"submit\" disabled={isCreating}>\n                  {isCreating ? '保存中...' : editingAuthor ? '更新' : '创建'}\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {/* 作者列表 */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n        {authors.map((author) => (\n          <Card \n            key={author.id} \n            className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n            onClick={() => onAuthorSelected?.(author)}\n          >\n            <CardHeader className=\"pb-3\">\n              <div className=\"flex items-center space-x-3\">\n                <Avatar>\n                  <AvatarImage src={author.avatar_url || undefined} />\n                  <AvatarFallback>\n                    <User className=\"w-4 h-4\" />\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"flex-1\">\n                  <CardTitle className=\"text-lg\">{author.name}</CardTitle>\n                  {author.email && (\n                    <div className=\"flex items-center text-sm text-gray-500\">\n                      <Mail className=\"w-3 h-3 mr-1\" />\n                      {author.email}\n                    </div>\n                  )}\n                </div>\n                <div className=\"flex space-x-1\">\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleEdit(author);\n                    }}\n                  >\n                    <Edit className=\"w-3 h-3\" />\n                  </Button>\n                  <Button\n                    size=\"sm\"\n                    variant=\"outline\"\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      handleDelete(author);\n                    }}\n                  >\n                    <Trash2 className=\"w-3 h-3\" />\n                  </Button>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent>\n              {author.bio && (\n                <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                  {author.bio}\n                </p>\n              )}\n              \n              <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                <span>\n                  创建于 {new Date(author.created_at).toLocaleDateString('zh-CN')}\n                </span>\n                {author.website && (\n                  <div className=\"flex items-center\">\n                    <Globe className=\"w-3 h-3 mr-1\" />\n                    <span>有网站</span>\n                  </div>\n                )}\n              </div>\n              \n              {(author.social_links?.twitter || author.social_links?.linkedin || author.social_links?.github) && (\n                <div className=\"flex space-x-1 mt-2\">\n                  {author.social_links?.twitter && <Badge variant=\"outline\" className=\"text-xs\">Twitter</Badge>}\n                  {author.social_links?.linkedin && <Badge variant=\"outline\" className=\"text-xs\">LinkedIn</Badge>}\n                  {author.social_links?.github && <Badge variant=\"outline\" className=\"text-xs\">GitHub</Badge>}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {authors.length === 0 && (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <p className=\"text-gray-500 mb-4\">还没有添加任何作者</p>\n            <Button onClick={() => setIsDialogOpen(true)}>\n              添加第一个作者\n            </Button>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAVA;;;;;;;;;;;AAiBe,SAAS,cAAc,EAAE,gBAAgB,EAAsB;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,KAAK;QACL,OAAO;QACP,SAAS;QACT,YAAY;QACZ,cAAc;YACZ,SAAS;YACT,UAAU;YACV,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,SAAS;YACT,YAAY;YACZ,cAAc;gBACZ,SAAS;gBACT,UAAU;gBACV,QAAQ;YACV;QACF;QACA,iBAAiB;IACnB;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,iBAAiB;QACjB,YAAY;YACV,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,GAAG,IAAI;YACnB,OAAO,OAAO,KAAK,IAAI;YACvB,SAAS,OAAO,OAAO,IAAI;YAC3B,YAAY,OAAO,UAAU,IAAI;YACjC,cAAc;gBACZ,SAAS,OAAO,YAAY,EAAE,WAAW;gBACzC,UAAU,OAAO,YAAY,EAAE,YAAY;gBAC3C,QAAQ,OAAO,YAAY,EAAE,UAAU;YACzC;QACF;QACA,gBAAgB;IAClB;IAEA,UAAU;IACV,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,MAAM;YACN;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,MAAM,gBAAgB,CAAC,aAAa,EAAE,cAAc,EAAE,EAAE,GAAG;YACjE,MAAM,SAAS,gBAAgB,QAAQ;YAEvC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YAEN,OAAO;YACP;YACA,gBAAgB;YAEhB,MAAM,gBAAgB,YAAY;QAEpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,cAAc;QAChB;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG;YACxD;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;gBACxD,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YACN,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;;;;;IAIjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCAEnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc,CAAC;4BACzC,gBAAgB;4BAChB,IAAI,CAAC,MAAM;wBACb;;0CACE,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;8CAAC;;;;;;;;;;;0CAEV,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DACT,gBAAgB,SAAS;;;;;;0DAE5B,8OAAC,kIAAA,CAAA,oBAAiB;0DACf,gBAAgB,WAAW;;;;;;;;;;;;kDAIhC,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACjE,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAIZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC;gEAClE,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,SAAS,GAAG;wDACnB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAChE,aAAY;wDACZ,MAAM;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,OAAO;gEACvB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACpE,aAAY;;;;;;;;;;;;kEAIhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,UAAU;gEAC1B,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACvE,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,YAAY,CAAC,OAAO;gEACpC,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,cAAc;4EAAE,GAAG,SAAS,YAAY;4EAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACpE;gEACA,aAAY;;;;;;0EAEd,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,YAAY,CAAC,QAAQ;gEACrC,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,cAAc;4EAAE,GAAG,SAAS,YAAY;4EAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACrE;gEACA,aAAY;;;;;;0EAEd,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,YAAY,CAAC,MAAM;gEACnC,UAAU,CAAC,IAAM,YAAY;wEAC3B,GAAG,QAAQ;wEACX,cAAc;4EAAE,GAAG,SAAS,YAAY;4EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACnE;gEACA,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,UAAU;kEAC7B,aAAa,WAAW,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS5D,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;wBAEH,WAAU;wBACV,SAAS,IAAM,mBAAmB;;0CAElC,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;;8DACL,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,OAAO,UAAU,IAAI;;;;;;8DACvC,8OAAC,kIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,OAAO,IAAI;;;;;;gDAC1C,OAAO,KAAK,kBACX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,OAAO,KAAK;;;;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,WAAW;oDACb;8DAEA,cAAA,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,aAAa;oDACf;8DAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC,gIAAA,CAAA,cAAW;;oCACT,OAAO,GAAG,kBACT,8OAAC;wCAAE,WAAU;kDACV,OAAO,GAAG;;;;;;kDAIf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAK;oDACC,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB,CAAC;;;;;;;4CAErD,OAAO,OAAO,kBACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;oCAKX,CAAC,OAAO,YAAY,EAAE,WAAW,OAAO,YAAY,EAAE,YAAY,OAAO,YAAY,EAAE,MAAM,mBAC5F,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,YAAY,EAAE,yBAAW,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;4CAC7E,OAAO,YAAY,EAAE,0BAAY,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;4CAC9E,OAAO,YAAY,EAAE,wBAAU,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;uBApE9E,OAAO,EAAE;;;;;;;;;;YA4EnB,QAAQ,MAAM,KAAK,mBAClB,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,gBAAgB;sCAAO;;;;;;;;;;;;;;;;;;;;;;;AAQ1D", "debugId": null}}, {"offset": {"line": 3474, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3536, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/prompt/PromptDebugger.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Badge } from '@/components/ui/badge';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';\nimport { Play, Save, Edit, Trash2, Clock } from 'lucide-react';\nimport type { PromptTemplate } from '@/types/blog';\n\nexport default function PromptDebugger() {\n  const [prompts, setPrompts] = useState<PromptTemplate[]>([]);\n  const [selectedPrompt, setSelectedPrompt] = useState<PromptTemplate | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isTesting, setIsTesting] = useState(false);\n  const [isSaving, setIsSaving] = useState(false);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [editingPrompt, setEditingPrompt] = useState<PromptTemplate | null>(null);\n  \n  const [testPrompt, setTestPrompt] = useState('');\n  const [testType, setTestType] = useState<'generation' | 'seo' | 'summary'>('generation');\n  const [testResult, setTestResult] = useState<any>(null);\n  \n  const [formData, setFormData] = useState({\n    name: '',\n    description: '',\n    template: '',\n    variables: [] as string[],\n    type: 'generation' as 'generation' | 'seo' | 'summary'\n  });\n\n  // 加载Prompt模板列表\n  const loadPrompts = async () => {\n    try {\n      const response = await fetch('/api/prompts');\n      const result = await response.json();\n      \n      if (result.success) {\n        setPrompts(result.data);\n      }\n    } catch (error) {\n      console.error('Failed to load prompts:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadPrompts();\n  }, []);\n\n  // 测试Prompt\n  const handleTest = async () => {\n    if (!testPrompt) {\n      alert('请输入要测试的Prompt');\n      return;\n    }\n\n    setIsTesting(true);\n    setTestResult(null);\n    \n    try {\n      const response = await fetch('/api/prompts/test', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          prompt: testPrompt,\n          type: testType\n        }),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Test failed');\n      }\n\n      setTestResult(result.data);\n      \n    } catch (error) {\n      console.error('Prompt test error:', error);\n      alert(`测试失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsTesting(false);\n    }\n  };\n\n  // 选择模板\n  const handleSelectPrompt = (prompt: PromptTemplate) => {\n    setSelectedPrompt(prompt);\n    setTestPrompt(prompt.template);\n    setTestType(prompt.type);\n  };\n\n  // 重置表单\n  const resetForm = () => {\n    setFormData({\n      name: '',\n      description: '',\n      template: '',\n      variables: [],\n      type: 'generation'\n    });\n    setEditingPrompt(null);\n  };\n\n  // 编辑模板\n  const handleEdit = (prompt: PromptTemplate) => {\n    setEditingPrompt(prompt);\n    setFormData({\n      name: prompt.name,\n      description: prompt.description || '',\n      template: prompt.template,\n      variables: prompt.variables || [],\n      type: prompt.type\n    });\n    setIsDialogOpen(true);\n  };\n\n  // 保存模板\n  const handleSave = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!formData.name || !formData.template) {\n      alert('请输入模板名称和内容');\n      return;\n    }\n\n    setIsSaving(true);\n    \n    try {\n      const url = editingPrompt ? `/api/prompts/${editingPrompt.id}` : '/api/prompts';\n      const method = editingPrompt ? 'PUT' : 'POST';\n      \n      const response = await fetch(url, {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Save failed');\n      }\n\n      // 重新加载模板列表\n      await loadPrompts();\n      \n      // 重置表单\n      resetForm();\n      setIsDialogOpen(false);\n      \n      alert(editingPrompt ? '模板更新成功！' : '模板创建成功！');\n      \n    } catch (error) {\n      console.error('Prompt save error:', error);\n      alert(`保存失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // 删除模板\n  const handleDelete = async (prompt: PromptTemplate) => {\n    if (!confirm(`确定要删除模板\"${prompt.name}\"吗？`)) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/prompts/${prompt.id}`, {\n        method: 'DELETE',\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'Delete failed');\n      }\n\n      // 重新加载模板列表\n      await loadPrompts();\n      \n      // 如果删除的是当前选中的模板，清空选择\n      if (selectedPrompt?.id === prompt.id) {\n        setSelectedPrompt(null);\n        setTestPrompt('');\n      }\n      \n      alert('模板删除成功！');\n      \n    } catch (error) {\n      console.error('Prompt deletion error:', error);\n      alert(`删除失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardContent className=\"p-6\">\n          <p className=\"text-center text-gray-500\">加载中...</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-2xl font-bold\">Prompt调试工具</h1>\n        \n        <Dialog open={isDialogOpen} onOpenChange={(open) => {\n          setIsDialogOpen(open);\n          if (!open) resetForm();\n        }}>\n          <DialogTrigger asChild>\n            <Button>\n              <Save className=\"w-4 h-4 mr-2\" />\n              新建模板\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-2xl\">\n            <DialogHeader>\n              <DialogTitle>\n                {editingPrompt ? '编辑模板' : '新建模板'}\n              </DialogTitle>\n              <DialogDescription>\n                {editingPrompt ? '修改Prompt模板' : '创建一个新的Prompt模板'}\n              </DialogDescription>\n            </DialogHeader>\n            \n            <form onSubmit={handleSave} className=\"space-y-4\">\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">模板名称 *</label>\n                  <Input\n                    value={formData.name}\n                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}\n                    placeholder=\"例如：基础博文生成\"\n                    required\n                  />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">类型 *</label>\n                  <Select\n                    value={formData.type}\n                    onValueChange={(value: 'generation' | 'seo' | 'summary') =>\n                      setFormData({ ...formData, type: value })\n                    }\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"generation\">博文生成</SelectItem>\n                      <SelectItem value=\"seo\">SEO优化</SelectItem>\n                      <SelectItem value=\"summary\">系列总结</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">描述</label>\n                <Input\n                  value={formData.description}\n                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                  placeholder=\"简单描述这个模板的用途\"\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">模板内容 *</label>\n                <Textarea\n                  value={formData.template}\n                  onChange={(e) => setFormData({ ...formData, template: e.target.value })}\n                  placeholder=\"输入Prompt模板，使用{变量名}表示变量\"\n                  rows={10}\n                  required\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <label className=\"text-sm font-medium\">变量列表（用逗号分隔）</label>\n                <Input\n                  value={formData.variables.join(', ')}\n                  onChange={(e) => {\n                    const variables = e.target.value.split(',').map(v => v.trim()).filter(v => v);\n                    setFormData({ ...formData, variables });\n                  }}\n                  placeholder=\"例如：input_type, input_content, language\"\n                />\n              </div>\n              \n              <div className=\"flex justify-end space-x-2\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setIsDialogOpen(false)}\n                >\n                  取消\n                </Button>\n                <Button type=\"submit\" disabled={isSaving}>\n                  {isSaving ? '保存中...' : editingPrompt ? '更新' : '创建'}\n                </Button>\n              </div>\n            </form>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      <Tabs defaultValue=\"test\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger value=\"test\">测试Prompt</TabsTrigger>\n          <TabsTrigger value=\"templates\">模板管理</TabsTrigger>\n        </TabsList>\n\n        {/* 测试标签页 */}\n        <TabsContent value=\"test\" className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* 左侧：Prompt输入和设置 */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Prompt测试</CardTitle>\n                <CardDescription>\n                  输入或选择Prompt模板进行测试\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">类型</label>\n                  <Select\n                    value={testType}\n                    onValueChange={(value: 'generation' | 'seo' | 'summary') =>\n                      setTestType(value)\n                    }\n                  >\n                    <SelectTrigger>\n                      <SelectValue />\n                    </SelectTrigger>\n                    <SelectContent>\n                      <SelectItem value=\"generation\">博文生成</SelectItem>\n                      <SelectItem value=\"seo\">SEO优化</SelectItem>\n                      <SelectItem value=\"summary\">系列总结</SelectItem>\n                    </SelectContent>\n                  </Select>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <label className=\"text-sm font-medium\">Prompt内容</label>\n                  <Textarea\n                    value={testPrompt}\n                    onChange={(e) => setTestPrompt(e.target.value)}\n                    placeholder=\"输入要测试的Prompt...\"\n                    rows={12}\n                  />\n                </div>\n                \n                <Button\n                  onClick={handleTest}\n                  disabled={isTesting}\n                  className=\"w-full\"\n                >\n                  <Play className=\"w-4 h-4 mr-2\" />\n                  {isTesting ? '测试中...' : '运行测试'}\n                </Button>\n              </CardContent>\n            </Card>\n\n            {/* 右侧：测试结果 */}\n            <Card>\n              <CardHeader>\n                <CardTitle>测试结果</CardTitle>\n                {testResult && (\n                  <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                    <Clock className=\"w-4 h-4\" />\n                    <span>耗时: {testResult.duration}ms</span>\n                    <span>•</span>\n                    <span>{new Date(testResult.timestamp).toLocaleString('zh-CN')}</span>\n                  </div>\n                )}\n              </CardHeader>\n              <CardContent>\n                {isTesting ? (\n                  <div className=\"flex items-center justify-center py-12\">\n                    <div className=\"text-center\">\n                      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n                      <p className=\"text-gray-500\">AI正在生成结果...</p>\n                    </div>\n                  </div>\n                ) : testResult ? (\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-gray-50 p-4 rounded-lg\">\n                      <pre className=\"whitespace-pre-wrap text-sm\">\n                        {testResult.result}\n                      </pre>\n                    </div>\n                  </div>\n                ) : (\n                  <div className=\"text-center py-12\">\n                    <p className=\"text-gray-500\">点击\"运行测试\"查看结果</p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </div>\n        </TabsContent>\n\n        {/* 模板管理标签页 */}\n        <TabsContent value=\"templates\" className=\"space-y-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {prompts.map((prompt) => (\n              <Card \n                key={prompt.id}\n                className=\"cursor-pointer hover:shadow-lg transition-shadow\"\n                onClick={() => handleSelectPrompt(prompt)}\n              >\n                <CardHeader className=\"pb-3\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex-1\">\n                      <CardTitle className=\"text-lg\">{prompt.name}</CardTitle>\n                      <div className=\"flex items-center space-x-2 mt-1\">\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {prompt.type === 'generation' ? '博文生成' : \n                           prompt.type === 'seo' ? 'SEO优化' : '系列总结'}\n                        </Badge>\n                      </div>\n                    </div>\n                    <div className=\"flex space-x-1\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleEdit(prompt);\n                        }}\n                      >\n                        <Edit className=\"w-3 h-3\" />\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          handleDelete(prompt);\n                        }}\n                      >\n                        <Trash2 className=\"w-3 h-3\" />\n                      </Button>\n                    </div>\n                  </div>\n                </CardHeader>\n                <CardContent>\n                  {prompt.description && (\n                    <p className=\"text-sm text-gray-600 mb-3 line-clamp-2\">\n                      {prompt.description}\n                    </p>\n                  )}\n                  \n                  <div className=\"text-xs text-gray-500\">\n                    <p>变量: {(prompt.variables || []).length} 个</p>\n                    <p>创建于 {new Date(prompt.created_at).toLocaleDateString('zh-CN')}</p>\n                  </div>\n                  \n                  {(prompt.variables || []).length > 0 && (\n                    <div className=\"flex flex-wrap gap-1 mt-2\">\n                      {(prompt.variables || []).slice(0, 3).map((variable, index) => (\n                        <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                          {variable}\n                        </Badge>\n                      ))}\n                      {(prompt.variables || []).length > 3 && (\n                        <Badge variant=\"secondary\" className=\"text-xs\">\n                          +{(prompt.variables || []).length - 3}\n                        </Badge>\n                      )}\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {prompts.length === 0 && (\n            <Card>\n              <CardContent className=\"p-12 text-center\">\n                <p className=\"text-gray-500 mb-4\">还没有创建任何Prompt模板</p>\n                <Button onClick={() => setIsDialogOpen(true)}>\n                  创建第一个模板\n                </Button>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAE1E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;IAC3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,UAAU;QACV,WAAW,EAAE;QACb,MAAM;IACR;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,WAAW,OAAO,IAAI;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,WAAW;IACX,MAAM,aAAa;QACjB,IAAI,CAAC,YAAY;YACf,MAAM;YACN;QACF;QAEA,aAAa;QACb,cAAc;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,MAAM;gBACR;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,cAAc,OAAO,IAAI;QAE3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;IACP,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,cAAc,OAAO,QAAQ;QAC7B,YAAY,OAAO,IAAI;IACzB;IAEA,OAAO;IACP,MAAM,YAAY;QAChB,YAAY;YACV,MAAM;YACN,aAAa;YACb,UAAU;YACV,WAAW,EAAE;YACb,MAAM;QACR;QACA,iBAAiB;IACnB;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,iBAAiB;QACjB,YAAY;YACV,MAAM,OAAO,IAAI;YACjB,aAAa,OAAO,WAAW,IAAI;YACnC,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS,IAAI,EAAE;YACjC,MAAM,OAAO,IAAI;QACnB;QACA,gBAAgB;IAClB;IAEA,OAAO;IACP,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,QAAQ,EAAE;YACxC,MAAM;YACN;QACF;QAEA,YAAY;QAEZ,IAAI;YACF,MAAM,MAAM,gBAAgB,CAAC,aAAa,EAAE,cAAc,EAAE,EAAE,GAAG;YACjE,MAAM,SAAS,gBAAgB,QAAQ;YAEvC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YAEN,OAAO;YACP;YACA,gBAAgB;YAEhB,MAAM,gBAAgB,YAAY;QAEpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE,SAAU;YACR,YAAY;QACd;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG;YACzC;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;gBACxD,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW;YACX,MAAM;YAEN,qBAAqB;YACrB,IAAI,gBAAgB,OAAO,OAAO,EAAE,EAAE;gBACpC,kBAAkB;gBAClB,cAAc;YAChB;YAEA,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClE;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;;;;;;IAIjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCAEnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAc,cAAc,CAAC;4BACzC,gBAAgB;4BAChB,IAAI,CAAC,MAAM;wBACb;;0CACE,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;;sDACL,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,8OAAC,kIAAA,CAAA,eAAY;;0DACX,8OAAC,kIAAA,CAAA,cAAW;0DACT,gBAAgB,SAAS;;;;;;0DAE5B,8OAAC,kIAAA,CAAA,oBAAiB;0DACf,gBAAgB,eAAe;;;;;;;;;;;;kDAIpC,8OAAC;wCAAK,UAAU;wCAAY,WAAU;;0DACpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,YAAY;wEAAE,GAAG,QAAQ;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC;gEACjE,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAIZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAAsB;;;;;;0EACvC,8OAAC,kIAAA,CAAA,SAAM;gEACL,OAAO,SAAS,IAAI;gEACpB,eAAe,CAAC,QACd,YAAY;wEAAE,GAAG,QAAQ;wEAAE,MAAM;oEAAM;;kFAGzC,8OAAC,kIAAA,CAAA,gBAAa;kFACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;;0FACZ,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAa;;;;;;0FAC/B,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAM;;;;;;0FACxB,8OAAC,kIAAA,CAAA,aAAU;gFAAC,OAAM;0FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE,aAAY;wDACZ,MAAM;wDACN,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAAsB;;;;;;kEACvC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,SAAS,SAAS,CAAC,IAAI,CAAC;wDAC/B,UAAU,CAAC;4DACT,MAAM,YAAY,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,MAAM,CAAC,CAAA,IAAK;4DAC3E,YAAY;gEAAE,GAAG,QAAQ;gEAAE;4DAAU;wDACvC;wDACA,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,SAAS,IAAM,gBAAgB;kEAChC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAS,UAAU;kEAC7B,WAAW,WAAW,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1D,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAO,WAAU;;kCAClC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;0CAC1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAY;;;;;;;;;;;;kCAIjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;kCAClC,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,gIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAInB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAsB;;;;;;sEACvC,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO;4DACP,eAAe,CAAC,QACd,YAAY;;8EAGd,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,8OAAC,kIAAA,CAAA,gBAAa;;sFACZ,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAa;;;;;;sFAC/B,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,8OAAC,kIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAKlC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAAsB;;;;;;sEACvC,8OAAC,oIAAA,CAAA,WAAQ;4DACP,OAAO;4DACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,aAAY;4DACZ,MAAM;;;;;;;;;;;;8DAIV,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,YAAY,WAAW;;;;;;;;;;;;;;;;;;;8CAM9B,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;;8DACT,8OAAC,gIAAA,CAAA,YAAS;8DAAC;;;;;;gDACV,4BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEAAK;gEAAK,WAAW,QAAQ;gEAAC;;;;;;;sEAC/B,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,IAAI,KAAK,WAAW,SAAS,EAAE,cAAc,CAAC;;;;;;;;;;;;;;;;;;sDAI3D,8OAAC,gIAAA,CAAA,cAAW;sDACT,0BACC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;uDAG/B,2BACF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,WAAW,MAAM;;;;;;;;;;;;;;;qEAKxB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;;0CACvC,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAU;wCACV,SAAS,IAAM,mBAAmB;;0DAElC,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gIAAA,CAAA,YAAS;oEAAC,WAAU;8EAAW,OAAO,IAAI;;;;;;8EAC3C,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAChC,OAAO,IAAI,KAAK,eAAe,SAC/B,OAAO,IAAI,KAAK,QAAQ,UAAU;;;;;;;;;;;;;;;;;sEAIzC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,WAAW;oEACb;8EAEA,cAAA,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;8EAElB,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,aAAa;oEACf;8EAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK1B,8OAAC,gIAAA,CAAA,cAAW;;oDACT,OAAO,WAAW,kBACjB,8OAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;kEAIvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAE;oEAAK,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,MAAM;oEAAC;;;;;;;0EACxC,8OAAC;;oEAAE;oEAAK,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;oDAGxD,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,MAAM,GAAG,mBACjC,8OAAC;wDAAI,WAAU;;4DACZ,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACnD,8OAAC,iIAAA,CAAA,QAAK;oEAAa,SAAQ;oEAAY,WAAU;8EAC9C;mEADS;;;;;4DAIb,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,MAAM,GAAG,mBACjC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;;oEAAU;oEAC3C,CAAC,OAAO,SAAS,IAAI,EAAE,EAAE,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;uCA5DzC,OAAO,EAAE;;;;;;;;;;4BAsEnB,QAAQ,MAAM,KAAK,mBAClB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS,IAAM,gBAAgB;sDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D", "debugId": null}}, {"offset": {"line": 4658, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/blog/SEOOptimizer.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Badge } from '@/components/ui/badge';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { SUPPORTED_LANGUAGES } from '@/types/blog';\nimport type { Language } from '@/types/blog';\n\ninterface SEOOptimizerProps {\n  initialTitle?: string;\n  initialContent?: string;\n  initialLanguage?: Language;\n  onSEOGenerated?: (seoInfo: any) => void;\n}\n\nexport default function SEOOptimizer({ \n  initialTitle = '', \n  initialContent = '', \n  initialLanguage = 'zh-CN',\n  onSEOGenerated \n}: SEOOptimizerProps) {\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [formData, setFormData] = useState({\n    title: initialTitle,\n    content: initialContent,\n    language: initialLanguage,\n  });\n  const [seoInfo, setSeoInfo] = useState<any>(null);\n\n  const handleGenerate = async () => {\n    if (!formData.title || !formData.content) {\n      alert('请输入标题和内容');\n      return;\n    }\n\n    setIsGenerating(true);\n    \n    try {\n      const response = await fetch('/api/blog/seo', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'SEO generation failed');\n      }\n\n      setSeoInfo(result.data);\n      \n      if (onSEOGenerated) {\n        onSEOGenerated(result.data);\n      }\n\n    } catch (error) {\n      console.error('SEO generation error:', error);\n      alert(`SEO生成失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle>SEO信息生成器</CardTitle>\n          <CardDescription>\n            为您的博文生成SEO优化的标题、描述、关键词和标签\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* 标题输入 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">博文标题</label>\n            <Input\n              value={formData.title}\n              onChange={(e) => setFormData({ ...formData, title: e.target.value })}\n              placeholder=\"输入博文标题\"\n            />\n          </div>\n\n          {/* 内容输入 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">博文内容</label>\n            <Textarea\n              value={formData.content}\n              onChange={(e) => setFormData({ ...formData, content: e.target.value })}\n              placeholder=\"输入博文内容（前2000字符用于SEO分析）\"\n              rows={6}\n            />\n          </div>\n\n          {/* 语言选择 */}\n          <div className=\"space-y-2\">\n            <label className=\"text-sm font-medium\">语言</label>\n            <Select\n              value={formData.language}\n              onValueChange={(value: Language) =>\n                setFormData({ ...formData, language: value })\n              }\n            >\n              <SelectTrigger>\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {SUPPORTED_LANGUAGES.map((lang) => (\n                  <SelectItem key={lang.code} value={lang.code}>\n                    {lang.name}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* 生成按钮 */}\n          <Button\n            onClick={handleGenerate}\n            disabled={isGenerating}\n            className=\"w-full\"\n          >\n            {isGenerating ? '正在生成SEO信息...' : '生成SEO信息'}\n          </Button>\n        </CardContent>\n      </Card>\n\n      {/* SEO信息显示 */}\n      {seoInfo && (\n        <Card>\n          <CardHeader>\n            <CardTitle>生成的SEO信息</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div>\n              <label className=\"text-sm font-medium text-gray-600\">SEO标题</label>\n              <div className=\"bg-gray-50 p-3 rounded mt-1\">\n                <p className=\"text-sm\">{seoInfo.seo_title}</p>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  长度: {seoInfo.seo_title.length} 字符\n                </p>\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium text-gray-600\">SEO描述</label>\n              <div className=\"bg-gray-50 p-3 rounded mt-1\">\n                <p className=\"text-sm\">{seoInfo.seo_description}</p>\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  长度: {seoInfo.seo_description.length} 字符\n                </p>\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium text-gray-600\">关键词</label>\n              <div className=\"flex flex-wrap gap-2 mt-2\">\n                {seoInfo.seo_keywords.map((keyword: string, index: number) => (\n                  <Badge key={index} variant=\"secondary\">\n                    {keyword}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium text-gray-600\">标签</label>\n              <div className=\"flex flex-wrap gap-2 mt-2\">\n                {seoInfo.tags.map((tag: string, index: number) => (\n                  <Badge key={index} variant=\"outline\">\n                    {tag}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"text-sm font-medium text-gray-600\">分类</label>\n              <div className=\"bg-gray-50 p-3 rounded mt-1\">\n                <p className=\"text-sm\">{seoInfo.category}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAmBe,SAAS,aAAa,EACnC,eAAe,EAAE,EACjB,iBAAiB,EAAE,EACnB,kBAAkB,OAAO,EACzB,cAAc,EACI;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,SAAS;QACT,UAAU;IACZ;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAE5C,MAAM,iBAAiB;QACrB,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,OAAO,EAAE;YACxC,MAAM;YACN;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,WAAW,OAAO,IAAI;YAEtB,IAAI,gBAAgB;gBAClB,eAAe,OAAO,IAAI;YAC5B;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,CAAC,SAAS,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACrE,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;;0CACT,8OAAC,gIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,8OAAC,gIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CAErB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAClE,aAAY;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8OAAC,oIAAA,CAAA,WAAQ;wCACP,OAAO,SAAS,OAAO;wCACvB,UAAU,CAAC,IAAM,YAAY;gDAAE,GAAG,QAAQ;gDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACpE,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,OAAO,SAAS,QAAQ;wCACxB,eAAe,CAAC,QACd,YAAY;gDAAE,GAAG,QAAQ;gDAAE,UAAU;4CAAM;;0DAG7C,8OAAC,kIAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;0DACX,oHAAA,CAAA,sBAAmB,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAiB,OAAO,KAAK,IAAI;kEACzC,KAAK,IAAI;uDADK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0CASlC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,iBAAiB;;;;;;;;;;;;;;;;;;YAMtC,yBACC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAW,QAAQ,SAAS;;;;;;0DACzC,8OAAC;gDAAE,WAAU;;oDAA6B;oDACnC,QAAQ,SAAS,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;0CAKpC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAW,QAAQ,eAAe;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;;oDAA6B;oDACnC,QAAQ,eAAe,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;0CAK1C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,SAAiB,sBAC1C,8OAAC,iIAAA,CAAA,QAAK;gDAAa,SAAQ;0DACxB;+CADS;;;;;;;;;;;;;;;;0CAOlB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAa,sBAC9B,8OAAC,iIAAA,CAAA,QAAK;gDAAa,SAAQ;0DACxB;+CADS;;;;;;;;;;;;;;;;0CAOlB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAoC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAW,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxD", "debugId": null}}, {"offset": {"line": 5107, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/components/ConfigStatus.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { CheckCircle, XCircle, AlertCircle } from 'lucide-react';\n\nexport default function ConfigStatus() {\n  const [qwenConfigured, setQwenConfigured] = useState<boolean | null>(null);\n\n  // 检查环境变量配置状态\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n  const isSupabaseConfigured = supabaseUrl &&\n    supabaseAnonKey &&\n    supabaseUrl !== 'your_supabase_url_here' &&\n    supabaseAnonKey !== 'your_supabase_anon_key_here';\n\n  // 检查 Qwen API 配置状态\n  useEffect(() => {\n    const checkQwenConfig = async () => {\n      try {\n        const response = await fetch('/api/config/check');\n        const result = await response.json();\n        setQwenConfigured(result.qwen_configured);\n      } catch (error) {\n        console.error('Failed to check Qwen config:', error);\n        setQwenConfigured(false);\n      }\n    };\n\n    checkQwenConfig();\n  }, []);\n\n  const configs = [\n    {\n      name: 'Supabase数据库',\n      status: isSupabaseConfigured ? 'configured' : 'not_configured',\n      description: isSupabaseConfigured ? '数据库连接已配置' : '需要配置Supabase URL和API密钥',\n      required: true\n    },\n    {\n      name: 'Qwen API',\n      status: qwenConfigured === null ? 'checking' : (qwenConfigured ? 'configured' : 'not_configured'),\n      description: qwenConfigured === null ? '正在检查配置...' :\n                  (qwenConfigured ? 'Qwen API密钥已配置' : '需要配置Qwen API密钥以使用AI生成功能'),\n      required: true\n    }\n  ];\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'configured':\n        return <CheckCircle className=\"w-5 h-5 text-green-600\" />;\n      case 'not_configured':\n        return <XCircle className=\"w-5 h-5 text-red-600\" />;\n      case 'checking':\n        return <AlertCircle className=\"w-5 h-5 text-blue-600 animate-pulse\" />;\n      default:\n        return <AlertCircle className=\"w-5 h-5 text-yellow-600\" />;\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'configured':\n        return <Badge className=\"bg-green-100 text-green-800\">已配置</Badge>;\n      case 'not_configured':\n        return <Badge variant=\"destructive\">未配置</Badge>;\n      case 'checking':\n        return <Badge variant=\"secondary\" className=\"animate-pulse\">检查中...</Badge>;\n      default:\n        return <Badge variant=\"secondary\">未知</Badge>;\n    }\n  };\n\n  return (\n    <Card className=\"mb-6\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <AlertCircle className=\"w-5 h-5 text-yellow-600\" />\n          <span>系统配置状态</span>\n        </CardTitle>\n        <CardDescription>\n          请确保以下服务已正确配置才能使用完整功能\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-4\">\n          {configs.map((config, index) => (\n            <div key={index} className=\"flex items-center justify-between p-3 border rounded-lg\">\n              <div className=\"flex items-center space-x-3\">\n                {getStatusIcon(config.status)}\n                <div>\n                  <h4 className=\"font-medium\">{config.name}</h4>\n                  <p className=\"text-sm text-gray-600\">{config.description}</p>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                {config.required && (\n                  <Badge variant=\"outline\" className=\"text-xs\">必需</Badge>\n                )}\n                {getStatusBadge(config.status)}\n              </div>\n            </div>\n          ))}\n        </div>\n        \n        {!isSupabaseConfigured && (\n          <div className=\"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <h4 className=\"font-medium text-yellow-800 mb-2\">配置说明</h4>\n            <div className=\"text-sm text-yellow-700 space-y-2\">\n              <p>1. 在 <a href=\"https://supabase.com\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline\">Supabase</a> 创建新项目</p>\n              <p>2. 在项目根目录的 <code className=\"bg-yellow-100 px-1 rounded\">.env.local</code> 文件中设置环境变量</p>\n              <p>3. 执行 <code className=\"bg-yellow-100 px-1 rounded\">database/schema.sql</code> 创建数据库表</p>\n              <p>4. 重启开发服务器</p>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAErE,aAAa;IACb,MAAM;IACN,MAAM;IAEN,MAAM,uBAAuB,eAC3B,mBACA,gBAAgB,4BAChB,oBAAoB;IAEtB,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,kBAAkB,OAAO,eAAe;YAC1C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,kBAAkB;YACpB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,UAAU;QACd;YACE,MAAM;YACN,QAAQ,uCAAuB,eAAe;YAC9C,aAAa,uCAAuB,aAAa;YACjD,UAAU;QACZ;QACA;YACE,MAAM;YACN,QAAQ,mBAAmB,OAAO,aAAc,iBAAiB,eAAe;YAChF,aAAa,mBAAmB,OAAO,cAC1B,iBAAiB,kBAAkB;YAChD,UAAU;QACZ;KACD;IAED,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BAA8B;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAc;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,WAAU;8BAAgB;;;;;;YAC9D;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAY;;;;;;QACtC;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,gIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAI,WAAU;;4CACZ,cAAc,OAAO,MAAM;0DAC5B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAe,OAAO,IAAI;;;;;;kEACxC,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;;kDAG5D,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,QAAQ,kBACd,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;4CAE9C,eAAe,OAAO,MAAM;;;;;;;;+BAZvB;;;;;;;;;;oBAkBb,CAAC,sCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAE;0DAAK,8OAAC;gDAAE,MAAK;gDAAuB,QAAO;gDAAS,KAAI;gDAAsB,WAAU;0DAAY;;;;;;4CAAY;;;;;;;kDACnH,8OAAC;;4CAAE;0DAAW,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;4CAAiB;;;;;;;kDAC5E,8OAAC;;4CAAE;0DAAM,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;4CAA0B;;;;;;;kDAChF,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 5453, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport BlogGenerator from '@/components/blog/BlogGenerator';\nimport BlogManager from '@/components/blog/BlogManager';\nimport BlogReview from '@/components/blog/BlogReview';\nimport SeriesManager from '@/components/series/SeriesManager';\nimport AuthorManager from '@/components/author/AuthorManager';\nimport PromptDebugger from '@/components/prompt/PromptDebugger';\nimport SEOOptimizer from '@/components/blog/SEOOptimizer';\nimport ConfigStatus from '@/components/ConfigStatus';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\n\nexport default function Home() {\n  const [generatedBlog, setGeneratedBlog] = useState<any>(null);\n\n  const handleBlogGenerated = (data: any) => {\n    setGeneratedBlog(data);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl font-bold text-slate-900 dark:text-slate-100 mb-4\">\n            AI博文自动生成系统\n          </h1>\n          <p className=\"text-lg text-slate-600 dark:text-slate-400\">\n            智能生成SEO优化的博文内容，支持系列管理和多语言\n          </p>\n        </div>\n\n        <ConfigStatus />\n\n        <Tabs defaultValue=\"generate\" className=\"max-w-6xl mx-auto\">\n          <TabsList className=\"grid w-full grid-cols-7\">\n            <TabsTrigger value=\"generate\">生成博文</TabsTrigger>\n            <TabsTrigger value=\"manage\">文章管理</TabsTrigger>\n            <TabsTrigger value=\"review\">审核管理</TabsTrigger>\n            <TabsTrigger value=\"series\">系列管理</TabsTrigger>\n            <TabsTrigger value=\"authors\">作者管理</TabsTrigger>\n            <TabsTrigger value=\"seo\">SEO工具</TabsTrigger>\n            <TabsTrigger value=\"prompts\">Prompt调试</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"generate\" className=\"mt-8\">\n            <BlogGenerator onGenerated={handleBlogGenerated} />\n          </TabsContent>\n\n          <TabsContent value=\"preview\" className=\"mt-8\">\n            {generatedBlog ? (\n              <BlogPreview\n                blogPost={generatedBlog.blog_post}\n                generatedContent={generatedBlog.generated_content}\n                onEdit={handleStartEdit}\n              />\n            ) : (\n              <div className=\"text-center py-12\">\n                <p className=\"text-slate-500 dark:text-slate-400\">\n                  请先生成博文内容\n                </p>\n              </div>\n            )}\n          </TabsContent>\n\n          <TabsContent value=\"edit\" className=\"mt-8\">\n            {generatedBlog ? (\n              <BlogEditor\n                blogPost={generatedBlog.blog_post}\n                onSave={handleSave}\n                onPublish={handlePublish}\n              />\n            ) : (\n              <div className=\"text-center py-12\">\n                <p className=\"text-slate-500 dark:text-slate-400\">\n                  请先生成博文内容\n                </p>\n              </div>\n            )}\n          </TabsContent>\n\n          <TabsContent value=\"review\" className=\"mt-8\">\n            <BlogReview />\n          </TabsContent>\n\n          <TabsContent value=\"series\" className=\"mt-8\">\n            <SeriesManager authorId=\"00000000-0000-0000-0000-000000000001\" />\n          </TabsContent>\n\n          <TabsContent value=\"authors\" className=\"mt-8\">\n            <AuthorManager />\n          </TabsContent>\n\n          <TabsContent value=\"seo\" className=\"mt-8\">\n            <SEOOptimizer />\n          </TabsContent>\n\n          <TabsContent value=\"prompts\" className=\"mt-8\">\n            <PromptDebugger />\n          </TabsContent>\n        </Tabs>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto mt-16\">\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow\">\n            <h3 className=\"text-xl font-semibold mb-3 text-slate-900 dark:text-slate-100\">\n              博文生成\n            </h3>\n            <p className=\"text-slate-600 dark:text-slate-400 mb-4\">\n              基于关键词、话题或标题智能生成高质量博文内容\n            </p>\n            <div className=\"text-sm text-slate-500 dark:text-slate-500\">\n              支持多语言 • AI驱动 • SEO优化\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow\">\n            <h3 className=\"text-xl font-semibold mb-3 text-slate-900 dark:text-slate-100\">\n              系列管理\n            </h3>\n            <p className=\"text-slate-600 dark:text-slate-400 mb-4\">\n              创建博文系列，保持内容连贯性和上下文关联\n            </p>\n            <div className=\"text-sm text-slate-500 dark:text-slate-500\">\n              智能关联 • 历史总结 • 连续性\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-slate-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow\">\n            <h3 className=\"text-xl font-semibold mb-3 text-slate-900 dark:text-slate-100\">\n              编辑发布\n            </h3>\n            <p className=\"text-slate-600 dark:text-slate-400 mb-4\">\n              实时预览、编辑优化，一键审核发布到数据库\n            </p>\n            <div className=\"text-sm text-slate-500 dark:text-slate-500\">\n              实时预览 • 富文本编辑 • 一键发布\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAExD,MAAM,sBAAsB,CAAC;QAC3B,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAG3E,8OAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,8OAAC,kIAAA,CAAA,UAAY;;;;;8BAEb,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAW,WAAU;;sCACtC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAU;;;;;;8CAC7B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAM;;;;;;8CACzB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAU;;;;;;;;;;;;sCAG/B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,2IAAA,CAAA,UAAa;gCAAC,aAAa;;;;;;;;;;;sCAG9B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACpC,8BACC,8OAAC;gCACC,UAAU,cAAc,SAAS;gCACjC,kBAAkB,cAAc,iBAAiB;gCACjD,QAAQ;;;;;qDAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;sCAOxD,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAO,WAAU;sCACjC,8BACC,8OAAC;gCACC,UAAU,cAAc,SAAS;gCACjC,QAAQ;gCACR,WAAW;;;;;qDAGb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;sCAOxD,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,8OAAC,wIAAA,CAAA,UAAU;;;;;;;;;;sCAGb,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;sCACpC,cAAA,8OAAC,6IAAA,CAAA,UAAa;gCAAC,UAAS;;;;;;;;;;;sCAG1B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,8OAAC,6IAAA,CAAA,UAAa;;;;;;;;;;sCAGhB,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;sCACjC,cAAA,8OAAC,0IAAA,CAAA,UAAY;;;;;;;;;;sCAGf,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAU,WAAU;sCACrC,cAAA,8OAAC,8IAAA,CAAA,UAAc;;;;;;;;;;;;;;;;8BAInB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;sCAK9D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,8OAAC;oCAAE,WAAU;8CAA0C;;;;;;8CAGvD,8OAAC;oCAAI,WAAU;8CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxE", "debugId": null}}]}