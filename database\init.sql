-- 简化的数据库初始化脚本
-- 请在Supabase SQL编辑器中执行此脚本

-- 作者表
CREATE TABLE IF NOT EXISTS authors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    bio TEXT,
    avatar_url TEXT,
    email VARCHAR(255) UNIQUE,
    website TEXT,
    social_links JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 博文系列表
CREATE TABLE IF NOT EXISTS blog_series (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    language VARCHAR(10) NOT NULL DEFAULT 'zh-CN',
    author_id UUID NOT NULL REFERENCES authors(id) ON DELETE CASCADE,
    posts_count INTEGER DEFAULT 0,
    summary TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 博文表
CREATE TABLE IF NOT EXISTS blog_posts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    language VARCHAR(10) NOT NULL DEFAULT 'zh-CN',
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    seo_title VARCHAR(500),
    seo_description TEXT,
    seo_keywords TEXT[],
    tags TEXT[],
    category VARCHAR(100),
    author_id UUID NOT NULL REFERENCES authors(id) ON DELETE CASCADE,
    series_id UUID REFERENCES blog_series(id) ON DELETE SET NULL,
    series_order INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE
);



-- 博文生成历史表
CREATE TABLE IF NOT EXISTS generation_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_type VARCHAR(20) NOT NULL,
    input_text TEXT NOT NULL,
    language VARCHAR(10) NOT NULL,
    series_id UUID REFERENCES blog_series(id) ON DELETE SET NULL,
    author_id UUID NOT NULL REFERENCES authors(id) ON DELETE CASCADE,
    prompt_used TEXT,
    response_data JSONB,
    blog_post_id UUID REFERENCES blog_posts(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入默认作者
INSERT INTO authors (id, name, bio, email) VALUES 
(
    '00000000-0000-0000-0000-000000000001',
    'AI博主',
    '专注于AI技术分享和知识传播的博主',
    '<EMAIL>'
) ON CONFLICT (id) DO NOTHING;

-- 插入默认Prompt模板
INSERT INTO prompt_templates (name, description, template, variables, type) VALUES 
(
    '基础博文生成',
    '基于关键词或话题生成博文的基础模板',
    '请基于以下信息生成一篇高质量的博文：

输入类型：{input_type}
输入内容：{input_content}
目标语言：{language}
{series_context}

要求：
1. 生成吸引人的标题
2. 内容结构清晰，包含引言、主体和结论
3. 内容长度适中（800-1500字）
4. 语言自然流畅
5. 包含实用的信息和见解

请以JSON格式返回结果，包含以下字段：
- title: 博文标题
- content: 博文内容（Markdown格式）
- summary: 博文摘要（100-200字）',
    ARRAY['input_type', 'input_content', 'language', 'series_context'],
    'generation'
),
(
    'SEO优化生成',
    '为博文生成SEO相关信息的模板',
    '请为以下博文生成SEO优化信息：

博文标题：{title}
博文内容：{content}
目标语言：{language}

要求：
1. 生成SEO友好的标题（50-60字符）
2. 生成吸引人的描述（150-160字符）
3. 提取5-8个相关关键词
4. 建议3-5个标签
5. 推荐合适的分类

请以JSON格式返回结果，包含以下字段：
- seo_title: SEO标题
- seo_description: SEO描述
- seo_keywords: 关键词数组
- tags: 标签数组
- category: 分类',
    ARRAY['title', 'content', 'language'],
    'seo'
),
(
    '系列总结生成',
    '为博文系列生成历史总结的模板',
    '请为以下博文系列生成一个简洁的历史总结：

系列名称：{series_name}
系列描述：{series_description}
已发布文章：
{published_posts}

要求：
1. 总结系列的主要主题和发展脉络
2. 突出关键观点和结论
3. 为后续文章提供上下文参考
4. 长度控制在200-300字

请直接返回总结内容，不需要JSON格式。',
    ARRAY['series_name', 'series_description', 'published_posts'],
    'summary'
) ON CONFLICT DO NOTHING;

-- 插入示例博文系列
INSERT INTO blog_series (id, name, description, language, author_id, summary) VALUES 
(
    '00000000-0000-0000-0000-000000000001',
    'AI技术入门系列',
    '面向初学者的AI技术介绍系列，从基础概念到实际应用',
    'zh-CN',
    '00000000-0000-0000-0000-000000000001',
    '本系列旨在为初学者提供AI技术的全面介绍，涵盖机器学习基础、深度学习原理、自然语言处理等核心领域。'
) ON CONFLICT (id) DO NOTHING;
