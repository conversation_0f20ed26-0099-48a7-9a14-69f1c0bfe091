{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/supabase/client.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\nimport type { Database } from './types';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n\n// 检查环境变量是否配置\nconst isConfigured = supabaseUrl &&\n  supabaseAnonKey &&\n  supabaseUrl !== 'your_supabase_url_here' &&\n  supabaseAnonKey !== 'your_supabase_anon_key_here';\n\n// 如果未配置，使用模拟客户端\nexport const supabase = isConfigured\n  ? createClient<Database>(supabaseUrl!, supabaseAnonKey!)\n  : null;\n\n// 服务端客户端（用于服务端操作）\nexport const createServerClient = () => {\n  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n\n  if (!isConfigured || !supabaseServiceKey || supabaseServiceKey === 'your_supabase_service_role_key_here') {\n    return null;\n  }\n\n  return createClient<Database>(supabaseUrl!, supabaseServiceKey);\n};\n"], "names": [], "mappings": ";;;;;AAGA,MAAM;AACN,MAAM;AAEN,aAAa;AACb,MAAM,eAAe,eACnB,mBACA,gBAAgB,4BAChB,oBAAoB;AAGf,MAAM,WAAW,sCACpB,0BACA;AAGG,MAAM,qBAAqB;IAChC,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;IAEhE,wCAA0G;QACxG,OAAO;IACT;;;AAGF", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/supabase/services.ts"], "sourcesContent": ["import { supabase, createServerClient } from './client';\nimport type { Database } from './types';\nimport type { BlogPost, BlogSeries, Author, PromptTemplate } from '@/types/blog';\n\ntype Tables = Database['public']['Tables'];\n\n// 检查Supabase是否已配置\nconst checkSupabaseConfig = () => {\n  if (!supabase) {\n    throw new Error('Supabase未配置。请在.env.local文件中设置正确的Supabase环境变量。');\n  }\n};\n\n// 作者相关操作\nexport const authorService = {\n  async getAll() {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) throw error;\n    return data as Author[];\n  },\n\n  async getById(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data as Author;\n  },\n\n  async create(author: Tables['authors']['Insert']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .insert(author)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Author;\n  },\n\n  async update(id: string, updates: Tables['authors']['Update']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('authors')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as Author;\n  },\n\n  async delete(id: string) {\n    checkSupabaseConfig();\n    const { error } = await supabase!\n      .from('authors')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  }\n};\n\n// 博文系列相关操作\nexport const seriesService = {\n  async getAll(authorId?: string) {\n    checkSupabaseConfig();\n    let query = supabase!\n      .from('blog_series')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (authorId) {\n      query = query.eq('author_id', authorId);\n    }\n\n    const { data, error } = await query;\n    if (error) throw error;\n    return data as BlogSeries[];\n  },\n\n  async getById(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_series')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data as BlogSeries;\n  },\n\n  async create(series: Tables['blog_series']['Insert']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_series')\n      .insert(series)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogSeries;\n  },\n\n  async update(id: string, updates: Tables['blog_series']['Update']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_series')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogSeries;\n  },\n\n  async delete(id: string) {\n    checkSupabaseConfig();\n    const { error } = await supabase!\n      .from('blog_series')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n\n  async getPostsInSeries(seriesId: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .select('*')\n      .eq('series_id', seriesId)\n      .order('series_order', { ascending: true });\n\n    if (error) throw error;\n    return data as BlogPost[];\n  }\n};\n\n// 博文相关操作\nexport const blogService = {\n  async getAll(filters?: {\n    authorId?: string;\n    seriesId?: string;\n    status?: 'draft' | 'published' | 'archived';\n    language?: string;\n  }) {\n    checkSupabaseConfig();\n    let query = supabase!\n      .from('blog_posts')\n      .select(`\n        *,\n        authors(name, avatar_url),\n        blog_series(name)\n      `)\n      .order('created_at', { ascending: false });\n\n    if (filters?.authorId) query = query.eq('author_id', filters.authorId);\n    if (filters?.seriesId) query = query.eq('series_id', filters.seriesId);\n    if (filters?.status) query = query.eq('status', filters.status);\n    if (filters?.language) query = query.eq('language', filters.language);\n\n    const { data, error } = await query;\n    if (error) throw error;\n    return data;\n  },\n\n  async getById(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .select(`\n        *,\n        authors(name, avatar_url),\n        blog_series(name)\n      `)\n      .eq('id', id)\n      .single();\n\n    if (error) throw error;\n    return data;\n  },\n\n  async create(post: Tables['blog_posts']['Insert']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .insert(post)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogPost;\n  },\n\n  async update(id: string, updates: Tables['blog_posts']['Update']) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogPost;\n  },\n\n  async delete(id: string) {\n    checkSupabaseConfig();\n    const { error } = await supabase!\n      .from('blog_posts')\n      .delete()\n      .eq('id', id);\n\n    if (error) throw error;\n  },\n\n  async publish(id: string) {\n    checkSupabaseConfig();\n    const { data, error } = await supabase!\n      .from('blog_posts')\n      .update({\n        status: 'published',\n        published_at: new Date().toISOString()\n      })\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) throw error;\n    return data as BlogPost;\n  }\n};\n\n// Prompt模板相关操作\nexport const promptService = {\n  async getAll(type?: 'generation' | 'seo' | 'summary') {\n    let query = supabase\n      .from('prompt_templates')\n      .select('*')\n      .order('created_at', { ascending: false });\n    \n    if (type) {\n      query = query.eq('type', type);\n    }\n    \n    const { data, error } = await query;\n    if (error) throw error;\n    return data as PromptTemplate[];\n  },\n\n  async getById(id: string) {\n    const { data, error } = await supabase\n      .from('prompt_templates')\n      .select('*')\n      .eq('id', id)\n      .single();\n    \n    if (error) throw error;\n    return data as PromptTemplate;\n  },\n\n  async create(template: Tables['prompt_templates']['Insert']) {\n    const { data, error } = await supabase\n      .from('prompt_templates')\n      .insert(template)\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data as PromptTemplate;\n  },\n\n  async update(id: string, updates: Tables['prompt_templates']['Update']) {\n    const { data, error } = await supabase\n      .from('prompt_templates')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n    \n    if (error) throw error;\n    return data as PromptTemplate;\n  },\n\n  async delete(id: string) {\n    const { error } = await supabase\n      .from('prompt_templates')\n      .delete()\n      .eq('id', id);\n    \n    if (error) throw error;\n  }\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AAMA,kBAAkB;AAClB,MAAM,sBAAsB;IAC1B,IAAI,CAAC,kIAAA,CAAA,WAAQ,EAAE;QACb,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM;QACJ;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,MAAmC;QAC9C;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAAoC;QAC3D;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,WACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,QAAO,QAAiB;QAC5B;QACA,IAAI,QAAQ,kIAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,eACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,UAAU;YACZ,QAAQ,MAAM,EAAE,CAAC,aAAa;QAChC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,MAAuC;QAClD;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAAwC;QAC/D;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,eACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,kBAAiB,QAAgB;QACrC;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,KAAK,CAAC,gBAAgB;YAAE,WAAW;QAAK;QAE3C,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,OAKZ;QACC;QACA,IAAI,QAAQ,kIAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,SAAS,UAAU,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,QAAQ;QACrE,IAAI,SAAS,UAAU,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,QAAQ;QACrE,IAAI,SAAS,QAAQ,QAAQ,MAAM,EAAE,CAAC,UAAU,QAAQ,MAAM;QAC9D,IAAI,SAAS,UAAU,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;QAEpE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;;MAIT,CAAC,EACA,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,IAAoC;QAC/C;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,MACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAAuC;QAC9D;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB;QACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,MAAM,SAAQ,EAAU;QACtB;QACA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,cACL,MAAM,CAAC;YACN,QAAQ;YACR,cAAc,IAAI,OAAO,WAAW;QACtC,GACC,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,QAAO,IAAuC;QAClD,IAAI,QAAQ,kIAAA,CAAA,WAAQ,CACjB,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,MAAM;YACR,QAAQ,MAAM,EAAE,CAAC,QAAQ;QAC3B;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAC9B,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,SAAQ,EAAU;QACtB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,QAA8C;QACzD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU,EAAE,OAA6C;QACpE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,oBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,QAAO,EAAU;QACrB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,kIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,oBACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;AACF", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/api/authors/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { authorService } from '@/lib/supabase/services';\n\n// 获取所有作者\nexport async function GET() {\n  try {\n    const authors = await authorService.getAll();\n    \n    return NextResponse.json({\n      success: true,\n      data: authors\n    });\n    \n  } catch (error) {\n    console.error('Authors fetch error:', error);\n    return NextResponse.json(\n      { \n        error: 'Failed to fetch authors',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n\n// 创建新作者\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    \n    // 验证请求数据\n    if (!body.name) {\n      return NextResponse.json(\n        { error: 'Missing required field: name' },\n        { status: 400 }\n      );\n    }\n    \n    const author = await authorService.create({\n      name: body.name,\n      bio: body.bio || null,\n      avatar_url: body.avatar_url || null,\n      email: body.email || null,\n      website: body.website || null,\n      social_links: body.social_links || {}\n    });\n    \n    return NextResponse.json({\n      success: true,\n      data: author\n    });\n    \n  } catch (error) {\n    console.error('Author creation error:', error);\n    return NextResponse.json(\n      { \n        error: 'Failed to create author',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,oIAAA,CAAA,gBAAa,CAAC,MAAM;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,SAAS;QACT,IAAI,CAAC,KAAK,IAAI,EAAE;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,oIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;YACxC,MAAM,KAAK,IAAI;YACf,KAAK,KAAK,GAAG,IAAI;YACjB,YAAY,KAAK,UAAU,IAAI;YAC/B,OAAO,KAAK,KAAK,IAAI;YACrB,SAAS,KAAK,OAAO,IAAI;YACzB,cAAc,KAAK,YAAY,IAAI,CAAC;QACtC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}