import { generateBlogPost, generateSEOInfo, generateSeriesSummary } from './qwen';
import { promptService, seriesService, blogService } from '../supabase/services';
import type { BlogGenerationRequest, BlogGenerationResponse, Language } from '@/types/blog';

// 替换模板变量的辅助函数
function replaceTemplateVariables(template: string, variables: Record<string, string>): string {
  let result = template;
  for (const [key, value] of Object.entries(variables)) {
    result = result.replace(new RegExp(`{${key}}`, 'g'), value);
  }
  return result;
}

// 解析JSON响应的辅助函数
function parseJSONResponse(content: string): any {
  try {
    // 尝试直接解析
    return JSON.parse(content);
  } catch {
    // 如果失败，尝试提取JSON部分
    const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || content.match(/{[\s\S]*}/);
    if (jsonMatch) {
      try {
        return JSON.parse(jsonMatch[1] || jsonMatch[0]);
      } catch {
        throw new Error('Failed to parse JSON response');
      }
    }
    throw new Error('No valid JSON found in response');
  }
}

// 获取系列上下文信息
async function getSeriesContext(seriesId: string): Promise<string> {
  try {
    const series = await seriesService.getById(seriesId);
    const posts = await seriesService.getPostsInSeries(seriesId);
    
    if (posts.length === 0) {
      return `这是系列"${series.name}"的第一篇文章。系列描述：${series.description}`;
    }
    
    const publishedPosts = posts.filter(post => post.status === 'published');
    const postSummaries = publishedPosts.map((post, index) => 
      `${index + 1}. ${post.title}: ${post.summary || '暂无摘要'}`
    ).join('\n');
    
    return `
系列背景：${series.name} - ${series.description}
系列总结：${series.summary || '暂无总结'}
已发布文章：
${postSummaries}

请基于以上系列背景和已发布文章，生成与系列主题相关且有逻辑连续性的新文章。`;
  } catch (error) {
    console.error('Error getting series context:', error);
    return '';
  }
}

// 主要的博文生成函数
export async function generateBlog(request: BlogGenerationRequest): Promise<BlogGenerationResponse> {
  try {
    // 获取生成模板
    const templates = await promptService.getAll('generation');
    const template = templates[0]; // 使用第一个生成模板
    
    if (!template) {
      throw new Error('No generation template found');
    }
    
    // 准备模板变量
    const variables: Record<string, string> = {
      input_type: request.type === 'keyword' ? '关键词' : request.type === 'topic' ? '话题' : '标题',
      input_content: request.input,
      language: request.language,
      series_context: request.series_id ? await getSeriesContext(request.series_id) : '这是一篇独立的博文。'
    };
    
    // 如果有额外指令，添加到模板中
    let finalTemplate = template.template;
    if (request.additional_instructions) {
      finalTemplate += `\n\n额外要求：${request.additional_instructions}`;
    }
    
    // 替换模板变量
    const prompt = replaceTemplateVariables(finalTemplate, variables);
    
    // 生成博文内容
    const response = await generateBlogPost(prompt);
    const parsedResponse = parseJSONResponse(response);
    
    // 验证响应格式
    if (!parsedResponse.title || !parsedResponse.content) {
      throw new Error('Invalid response format: missing title or content');
    }
    
    // 生成SEO信息
    const seoResponse = await generateSEOForBlog({
      title: parsedResponse.title,
      content: parsedResponse.content,
      language: request.language
    });
    
    return {
      title: parsedResponse.title,
      content: parsedResponse.content,
      summary: parsedResponse.summary || '',
      seo_title: seoResponse.seo_title,
      seo_description: seoResponse.seo_description,
      seo_keywords: seoResponse.seo_keywords,
      tags: seoResponse.tags,
      category: seoResponse.category
    };
    
  } catch (error) {
    console.error('Blog generation error:', error);
    throw new Error(`Failed to generate blog: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// 为博文生成SEO信息
export async function generateSEOForBlog(params: {
  title: string;
  content: string;
  language: string;
}): Promise<{
  seo_title: string;
  seo_description: string;
  seo_keywords: string[];
  tags: string[];
  category: string;
}> {
  try {
    // 获取SEO模板
    const templates = await promptService.getAll('seo');
    const template = templates[0];
    
    if (!template) {
      throw new Error('No SEO template found');
    }
    
    // 准备模板变量
    const variables = {
      title: params.title,
      content: params.content.substring(0, 2000), // 限制内容长度
      language: params.language
    };
    
    // 替换模板变量
    const prompt = replaceTemplateVariables(template.template, variables);
    
    // 生成SEO信息
    const response = await generateSEOInfo(prompt);
    const parsedResponse = parseJSONResponse(response);
    
    return {
      seo_title: parsedResponse.seo_title || params.title,
      seo_description: parsedResponse.seo_description || '',
      seo_keywords: Array.isArray(parsedResponse.seo_keywords) ? parsedResponse.seo_keywords : [],
      tags: Array.isArray(parsedResponse.tags) ? parsedResponse.tags : [],
      category: parsedResponse.category || '未分类'
    };
    
  } catch (error) {
    console.error('SEO generation error:', error);
    // 返回默认值而不是抛出错误
    return {
      seo_title: params.title,
      seo_description: '',
      seo_keywords: [],
      tags: [],
      category: '未分类'
    };
  }
}

// 更新系列总结
export async function updateSeriesSummary(seriesId: string): Promise<void> {
  try {
    const series = await seriesService.getById(seriesId);
    const posts = await seriesService.getPostsInSeries(seriesId);
    const publishedPosts = posts.filter(post => post.status === 'published');
    
    if (publishedPosts.length === 0) {
      return; // 没有已发布的文章，不需要更新总结
    }
    
    // 获取总结模板
    const templates = await promptService.getAll('summary');
    const template = templates[0];
    
    if (!template) {
      console.warn('No summary template found');
      return;
    }
    
    // 准备文章列表
    const postsList = publishedPosts.map((post, index) => 
      `${index + 1}. 《${post.title}》\n   摘要：${post.summary || '暂无摘要'}\n   主要内容：${post.content.substring(0, 200)}...`
    ).join('\n\n');
    
    // 准备模板变量
    const variables = {
      series_name: series.name,
      series_description: series.description || '',
      published_posts: postsList
    };
    
    // 替换模板变量
    const prompt = replaceTemplateVariables(template.template, variables);
    
    // 生成总结
    const summary = await generateSeriesSummary(prompt);
    
    // 更新系列总结
    await seriesService.update(seriesId, { summary });
    
  } catch (error) {
    console.error('Series summary update error:', error);
    // 不抛出错误，因为这不是关键功能
  }
}
