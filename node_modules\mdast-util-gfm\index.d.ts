import type {ToMarkdownOptions as FootnoteOptions} from 'mdast-util-gfm-footnote'
import type {Options as TableOptions} from 'mdast-util-gfm-table'

/**
 * Configuration for `gfmToMarkdown` from `mdast-util-gfm`.
 *
 * Currently supports options for `mdast-util-gfm-footnote` and
 * `mdast-util-gfm-table`.
 */
export interface Options extends FootnoteOptions, TableOptions {}

export {gfmFromMarkdown, gfmToMarkdown} from './lib/index.js'
