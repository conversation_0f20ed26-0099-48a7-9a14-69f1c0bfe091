{"name": "inline-style-parser", "version": "0.2.4", "description": "An inline style parser.", "main": "index.js", "scripts": {"build": "rollup --config --failAfterWarnings", "clean": "rm -rf dist", "lint": "eslint .", "lint:fix": "npm run lint -- --fix", "prepare": "husky", "prepublishOnly": "npm run lint && npm test && npm run build", "test": "jest", "test:ci": "CI=true jest --ci --colors --coverage --collectCoverageFrom=index.js", "test:esm": "node --test test/index.test.mjs", "test:watch": "jest --watch"}, "repository": {"type": "git", "url": "https://github.com/remarkablemark/inline-style-parser"}, "bugs": {"url": "https://github.com/remarkablemark/inline-style-parser/issues"}, "keywords": ["inline-style-parser", "inline-style", "style", "parser", "css"], "devDependencies": {"@commitlint/cli": "19.4.1", "@commitlint/config-conventional": "19.4.1", "@eslint/compat": "1.1.1", "@eslint/eslintrc": "3.1.0", "@eslint/js": "9.9.1", "@rollup/plugin-commonjs": "26.0.1", "@rollup/plugin-terser": "0.4.4", "css": "3.0.0", "eslint": "9.9.1", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-simple-import-sort": "12.1.1", "globals": "15.9.0", "husky": "9.1.5", "jest": "29.7.0", "lint-staged": "15.2.9", "prettier": "3.3.3", "rollup": "4.21.1"}, "files": ["/dist", "/index.d.ts"], "license": "MIT"}