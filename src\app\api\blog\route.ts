import { NextRequest, NextResponse } from 'next/server';
import { blogService } from '@/lib/supabase/services';

// 获取所有博文
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const authorId = searchParams.get('author_id');
    const seriesId = searchParams.get('series_id');
    const status = searchParams.get('status') as 'draft' | 'published' | 'archived' | null;
    const language = searchParams.get('language');
    
    const filters: any = {};
    if (authorId) filters.authorId = authorId;
    if (seriesId) filters.seriesId = seriesId;
    if (status) filters.status = status;
    if (language) filters.language = language;
    
    const blogs = await blogService.getAll(filters);
    
    return NextResponse.json({
      success: true,
      data: blogs
    });
    
  } catch (error) {
    console.error('Blogs fetch error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch blogs',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
