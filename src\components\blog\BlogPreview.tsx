'use client';

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, Eye } from 'lucide-react';
import type { BlogPost } from '@/types/blog';

interface BlogPreviewProps {
  blogPost: BlogPost;
  generatedContent?: {
    title: string;
    content: string;
    summary: string;
    seo_title: string;
    seo_description: string;
    seo_keywords: string[];
    tags: string[];
    category: string;
  };
  onEdit?: () => void;
}

export default function BlogPreview({ blogPost, generatedContent, onEdit }: BlogPreviewProps) {
  const content = generatedContent || blogPost;

  // 简单的Markdown渲染（基础版本）
  const renderMarkdown = (text: string) => {
    return text
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-semibold mt-6 mb-3">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>')
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
      .replace(/\n\n/g, '</p><p class="mb-4">')
      .replace(/\n/g, '<br>')
      .replace(/^(.*)$/gm, '<p class="mb-4">$1</p>');
  };

  return (
    <div className="space-y-6">
      {/* SEO信息预览 */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">SEO信息</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-600">SEO标题</label>
            <p className="text-sm bg-gray-50 p-2 rounded mt-1">
              {content.seo_title || content.title}
            </p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">SEO描述</label>
            <p className="text-sm bg-gray-50 p-2 rounded mt-1">
              {content.seo_description || '暂无描述'}
            </p>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">关键词</label>
            <div className="flex flex-wrap gap-1 mt-1">
              {(content.seo_keywords || []).map((keyword, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {keyword}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">标签</label>
            <div className="flex flex-wrap gap-1 mt-1">
              {(content.tags || []).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">分类</label>
            <p className="text-sm bg-gray-50 p-2 rounded mt-1">
              {content.category || '未分类'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 博文内容预览 */}
      <Card>
        <CardHeader>
          <CardTitle>{content.title}</CardTitle>
          {content.summary && (
            <CardDescription className="text-base">
              {content.summary}
            </CardDescription>
          )}
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <span>状态: {blogPost.status === 'draft' ? '草稿' : blogPost.status === 'published' ? '已发布' : '已归档'}</span>
            <span>•</span>
            <span>语言: {blogPost.language}</span>
            {blogPost.created_at && (
              <>
                <span>•</span>
                <span>创建时间: {new Date(blogPost.created_at).toLocaleDateString('zh-CN')}</span>
              </>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div 
            className="prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{ 
              __html: renderMarkdown(content.content) 
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}
