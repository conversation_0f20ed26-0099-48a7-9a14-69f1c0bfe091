// 博文相关类型定义

export interface BlogPost {
  id: string;
  title: string;
  content: string;
  summary: string;
  language: string;
  status: 'draft' | 'published' | 'archived';
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
  tags?: string[];
  category?: string;
  author_id: string;
  series_id?: string;
  series_order?: number;
  created_at: string;
  updated_at: string;
  published_at?: string;
}

export interface BlogSeries {
  id: string;
  name: string;
  description: string;
  language: string;
  author_id: string;
  posts_count: number;
  summary: string; // 系列历史总结
  created_at: string;
  updated_at: string;
}

export interface Author {
  id: string;
  name: string;
  bio?: string;
  avatar_url?: string;
  email?: string;
  website?: string;
  social_links?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  created_at: string;
  updated_at: string;
}

export interface BlogGenerationRequest {
  type: 'keyword' | 'topic' | 'title';
  input: string;
  language: string;
  series_id?: string;
  author_id: string;
  additional_instructions?: string;
}

export interface BlogGenerationResponse {
  title: string;
  content: string;
  summary: string;
  seo_title: string;
  seo_description: string;
  seo_keywords: string[];
  tags: string[];
  category: string;
}



export type Language = 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR' | 'es-ES' | 'fr-FR' | 'de-DE';

export const SUPPORTED_LANGUAGES: { code: Language; name: string }[] = [
  { code: 'zh-CN', name: '中文' },
  { code: 'en-US', name: 'English' },
  { code: 'ja-JP', name: '日本語' },
  { code: 'ko-KR', name: '한국어' },
  { code: 'es-ES', name: 'Español' },
  { code: 'fr-FR', name: 'Français' },
  { code: 'de-DE', name: 'Deutsch' },
];
