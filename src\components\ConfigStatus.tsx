'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, AlertCircle, RefreshCw, Settings, Database, Bot } from 'lucide-react';

export default function ConfigStatus() {
  const [qwenConfigured, setQwenConfigured] = useState<boolean | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 检查环境变量配置状态
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  const isSupabaseConfigured = supabaseUrl &&
    supabaseAnonKey &&
    supabaseUrl !== 'your_supabase_url_here' &&
    supabaseAnonKey !== 'your_supabase_anon_key_here';

  // 检查配置状态
  const checkConfigurations = async () => {
    setIsRefreshing(true);
    try {
      const response = await fetch('/api/config/check');
      const result = await response.json();
      setQwenConfigured(result.qwen_configured);
    } catch (error) {
      console.error('Failed to check config:', error);
      setQwenConfigured(false);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    checkConfigurations();
  }, []);

  const configs = [
    {
      name: 'Supabase数据库',
      icon: <Database className="w-5 h-5" />,
      status: isSupabaseConfigured ? 'configured' : 'not_configured',
      description: isSupabaseConfigured ? '数据库连接已配置，可以存储和管理博文数据' : '需要配置Supabase URL和API密钥以使用数据库功能',
      required: true,
      details: isSupabaseConfigured ? [
        `URL: ${supabaseUrl?.substring(0, 30)}...`,
        '匿名密钥已配置',
        '数据库表结构正常'
      ] : [
        '未配置数据库连接',
        '无法存储博文数据',
        '需要设置环境变量'
      ]
    },
    {
      name: 'Qwen API',
      icon: <Bot className="w-5 h-5" />,
      status: qwenConfigured === null ? 'checking' : (qwenConfigured ? 'configured' : 'not_configured'),
      description: qwenConfigured === null ? '正在检查配置...' :
                  (qwenConfigured ? 'Qwen API密钥已配置，可以使用AI生成功能' : '需要配置Qwen API密钥以使用AI生成功能'),
      required: true,
      details: qwenConfigured === null ? ['检查中...'] :
               (qwenConfigured ? [
                 '模型: qwen-plus-latest',
                 'API连接正常',
                 '支持博文生成和SEO优化'
               ] : [
                 '未配置API密钥',
                 '无法使用AI生成功能',
                 '需要设置QWEN_API_KEY'
               ])
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'configured':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'not_configured':
        return <XCircle className="w-5 h-5 text-red-600" />;
      case 'checking':
        return <AlertCircle className="w-5 h-5 text-blue-600 animate-pulse" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'configured':
        return <Badge className="bg-green-100 text-green-800">已配置</Badge>;
      case 'not_configured':
        return <Badge variant="destructive">未配置</Badge>;
      case 'checking':
        return <Badge variant="secondary" className="animate-pulse">检查中...</Badge>;
      default:
        return <Badge variant="secondary">未知</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">系统配置</h2>
          <p className="text-gray-600">管理系统配置和服务状态</p>
        </div>
        <Button
          onClick={checkConfigurations}
          disabled={isRefreshing}
          className="flex items-center space-x-2"
        >
          <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span>刷新状态</span>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5" />
            <span>服务配置状态</span>
          </CardTitle>
          <CardDescription>
            系统依赖的核心服务配置状态检查
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {configs.map((config, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-2">
                      {config.icon}
                      {getStatusIcon(config.status)}
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg">{config.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{config.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {config.required && (
                      <Badge variant="outline" className="text-xs">必需</Badge>
                    )}
                    {getStatusBadge(config.status)}
                  </div>
                </div>

                <div className="ml-10 space-y-1">
                  {config.details.map((detail, detailIndex) => (
                    <div key={detailIndex} className="text-sm text-gray-500 flex items-center space-x-2">
                      <span className="w-1 h-1 bg-gray-400 rounded-full"></span>
                      <span>{detail}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 配置指南 */}
      <Card>
        <CardHeader>
          <CardTitle>配置指南</CardTitle>
          <CardDescription>
            如何正确配置系统所需的服务
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div>
              <h4 className="font-semibold mb-3 flex items-center space-x-2">
                <Database className="w-4 h-4" />
                <span>Supabase 数据库配置</span>
              </h4>
              <div className="space-y-2 text-sm text-gray-600 ml-6">
                <p>1. 访问 <a href="https://supabase.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">Supabase官网</a> 创建新项目</p>
                <p>2. 在项目设置中获取 URL 和 anon key</p>
                <p>3. 在 <code className="bg-gray-100 px-2 py-1 rounded text-xs">.env.local</code> 文件中设置：</p>
                <div className="bg-gray-50 p-3 rounded-lg font-mono text-xs">
                  <div>NEXT_PUBLIC_SUPABASE_URL=your_supabase_url</div>
                  <div>NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key</div>
                  <div>SUPABASE_SERVICE_ROLE_KEY=your_service_role_key</div>
                </div>
                <p>4. 执行 <code className="bg-gray-100 px-2 py-1 rounded text-xs">database/init.sql</code> 创建数据库表</p>
                <p>5. 重启开发服务器</p>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3 flex items-center space-x-2">
                <Bot className="w-4 h-4" />
                <span>Qwen API 配置</span>
              </h4>
              <div className="space-y-2 text-sm text-gray-600 ml-6">
                <p>1. 访问 <a href="https://dashscope.aliyuncs.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 underline">阿里云DashScope</a> 获取API密钥</p>
                <p>2. 在 <code className="bg-gray-100 px-2 py-1 rounded text-xs">.env.local</code> 文件中设置：</p>
                <div className="bg-gray-50 p-3 rounded-lg font-mono text-xs">
                  <div>QWEN_API_KEY=sk-your_qwen_api_key</div>
                  <div>QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1</div>
                </div>
                <p>3. 重启开发服务器</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
