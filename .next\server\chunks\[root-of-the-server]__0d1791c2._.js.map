{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/ai/openai.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\nif (!process.env.OPENAI_API_KEY) {\n  throw new Error('Missing OpenAI API key');\n}\n\nexport const openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// 默认的AI配置\nexport const AI_CONFIG = {\n  model: 'gpt-4o-mini', // 使用更经济的模型\n  temperature: 0.7,\n  max_tokens: 4000,\n  top_p: 1,\n  frequency_penalty: 0,\n  presence_penalty: 0,\n};\n\n// 生成博文的函数\nexport async function generateBlogPost(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个专业的博客写手，擅长创作高质量、SEO友好的博文内容。请严格按照用户要求的格式返回结果。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: config.temperature,\n      max_tokens: config.max_tokens,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No content generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate content');\n  }\n}\n\n// 生成SEO信息的函数\nexport async function generateSEOInfo(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个SEO专家，擅长为博文生成优化的SEO信息。请严格按照JSON格式返回结果。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.3, // 降低温度以获得更一致的结果\n      max_tokens: 1000,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No SEO content generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate SEO info');\n  }\n}\n\n// 生成系列总结的函数\nexport async function generateSeriesSummary(prompt: string, config = AI_CONFIG) {\n  try {\n    const response = await openai.chat.completions.create({\n      model: config.model,\n      messages: [\n        {\n          role: 'system',\n          content: `你是一个内容总结专家，擅长为博文系列生成简洁而全面的总结。`\n        },\n        {\n          role: 'user',\n          content: prompt\n        }\n      ],\n      temperature: 0.5,\n      max_tokens: 500,\n      top_p: config.top_p,\n      frequency_penalty: config.frequency_penalty,\n      presence_penalty: config.presence_penalty,\n    });\n\n    const content = response.choices[0]?.message?.content;\n    if (!content) {\n      throw new Error('No summary generated');\n    }\n\n    return content;\n  } catch (error) {\n    console.error('OpenAI API error:', error);\n    throw new Error('Failed to generate series summary');\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC/B,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAGO,MAAM,YAAY;IACvB,OAAO;IACP,aAAa;IACb,YAAY;IACZ,OAAO;IACP,mBAAmB;IACnB,kBAAkB;AACpB;AAGO,eAAe,iBAAiB,MAAc,EAAE,SAAS,SAAS;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,gDAAgD,CAAC;gBAC7D;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa,OAAO,WAAW;YAC/B,YAAY,OAAO,UAAU;YAC7B,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,gBAAgB,MAAc,EAAE,SAAS,SAAS;IACtE,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,0CAA0C,CAAC;gBACvD;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;YACZ,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,sBAAsB,MAAc,EAAE,SAAS,SAAS;IAC5E,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO,OAAO,KAAK;YACnB,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS,CAAC,6BAA6B,CAAC;gBAC1C;gBACA;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,aAAa;YACb,YAAY;YACZ,OAAO,OAAO,KAAK;YACnB,mBAAmB,OAAO,iBAAiB;YAC3C,kBAAkB,OAAO,gBAAgB;QAC3C;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAC9C,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM,IAAI,MAAM;IAClB;AACF", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/api/test/openai/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { openai } from '@/lib/ai/openai';\n\nexport async function GET() {\n  try {\n    // 测试 OpenAI API 连接\n    const response = await openai.chat.completions.create({\n      model: 'gpt-4o-mini',\n      messages: [\n        {\n          role: 'user',\n          content: '请简单回复\"测试成功\"'\n        }\n      ],\n      max_tokens: 10,\n      temperature: 0\n    });\n\n    const content = response.choices[0]?.message?.content;\n    \n    return NextResponse.json({\n      success: true,\n      message: 'OpenAI API connection successful',\n      response: content,\n      model: response.model,\n      usage: response.usage\n    });\n    \n  } catch (error) {\n    console.error('OpenAI API test error:', error);\n    return NextResponse.json(\n      { \n        error: 'OpenAI API test failed',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,mBAAmB;QACnB,MAAM,WAAW,MAAM,4HAAA,CAAA,SAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBACR;oBACE,MAAM;oBACN,SAAS;gBACX;aACD;YACD,YAAY;YACZ,aAAa;QACf;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS;QAE9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,UAAU;YACV,OAAO,SAAS,KAAK;YACrB,OAAO,SAAS,KAAK;QACvB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}