# 数据库设置说明

## 快速设置步骤

### 1. 访问Supabase控制台
1. 打开 [Supabase控制台](https://supabase.com/dashboard)
2. 选择你的项目：`ohcnehqjrqzjlgbmjcck`

### 2. 执行数据库脚本
1. 在左侧菜单中点击 "SQL Editor"
2. 点击 "New query" 创建新查询
3. 复制 `database/init.sql` 文件的全部内容
4. 粘贴到SQL编辑器中
5. 点击 "Run" 执行脚本

### 3. 验证表创建
执行脚本后，你应该看到以下表被创建：
- `authors` - 作者表
- `blog_series` - 博文系列表  
- `blog_posts` - 博文表
- `prompt_templates` - Prompt模板表
- `generation_history` - 生成历史表

### 4. 检查初始数据
脚本会自动插入以下初始数据：
- 一个默认作者 "AI博主"
- 三个Prompt模板（博文生成、SEO优化、系列总结）
- 一个示例博文系列 "AI技术入门系列"

### 5. 设置行级安全策略（可选）
如果需要更严格的安全控制，可以执行以下SQL：

```sql
-- 启用RLS
ALTER TABLE authors ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_series ENABLE ROW LEVEL SECURITY;
ALTER TABLE blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE prompt_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE generation_history ENABLE ROW LEVEL SECURITY;

-- 允许读取已发布的内容
CREATE POLICY "Allow read published posts" ON blog_posts
    FOR SELECT USING (status = 'published');

-- 允许读取作者和系列信息
CREATE POLICY "Allow read authors" ON authors FOR SELECT USING (true);
CREATE POLICY "Allow read series" ON blog_series FOR SELECT USING (true);
CREATE POLICY "Allow read templates" ON prompt_templates FOR SELECT USING (true);

-- 允许所有操作（开发环境）
CREATE POLICY "Allow all operations" ON authors FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON blog_series FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON blog_posts FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON prompt_templates FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON generation_history FOR ALL USING (true);
```

## 完成设置

设置完成后，重启你的Next.js开发服务器，系统就可以正常使用了！

访问 http://localhost:3001 开始使用AI博文生成系统。
