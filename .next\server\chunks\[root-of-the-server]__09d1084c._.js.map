{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/api/config/check/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nexport async function GET() {\n  try {\n    // 检查 Qwen API 密钥是否配置\n    const qwenApiKey = process.env.QWEN_API_KEY;\n    const qwenConfigured = !!(qwenApiKey && qwenApiKey.startsWith('sk-'));\n    \n    // 检查 Supabase 配置\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\n    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    \n    const supabaseConfigured = !!(\n      supabaseUrl && \n      supabaseAnonKey && \n      supabaseServiceKey &&\n      supabaseUrl !== 'your_supabase_url_here' &&\n      supabaseAnonKey !== 'your_supabase_anon_key_here' &&\n      supabaseServiceKey !== 'your_supabase_service_role_key_here'\n    );\n\n    return NextResponse.json({\n      success: true,\n      qwen_configured: qwenConfigured,\n      supabase_configured: supabaseConfigured,\n      details: {\n        qwen: qwenConfigured ? 'Qwen API key is configured' : 'Qwen API key is missing or invalid',\n        supabase: supabaseConfigured ? 'Supabase is configured' : 'Supabase configuration is incomplete'\n      }\n    });\n    \n  } catch (error) {\n    console.error('Config check error:', error);\n    return NextResponse.json(\n      { \n        error: 'Failed to check configuration',\n        details: error instanceof Error ? error.message : 'Unknown error'\n      },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,IAAI;QACF,qBAAqB;QACrB,MAAM,aAAa,QAAQ,GAAG,CAAC,YAAY;QAC3C,MAAM,iBAAiB,CAAC,CAAC,CAAC,cAAc,WAAW,UAAU,CAAC,MAAM;QAEpE,iBAAiB;QACjB,MAAM;QACN,MAAM;QACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;QAEhE,MAAM,qBAAqB,CAAC,CAAC,CAC3B,eACA,mBACA,sBACA,gBAAgB,4BAChB,oBAAoB,iCACpB,uBAAuB,qCACzB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,iBAAiB;YACjB,qBAAqB;YACrB,SAAS;gBACP,MAAM,iBAAiB,+BAA+B;gBACtD,UAAU,qBAAqB,2BAA2B;YAC5D;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YACE,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GACA;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}