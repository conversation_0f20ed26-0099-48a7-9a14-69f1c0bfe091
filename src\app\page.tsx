'use client';

import { useState } from 'react';
import BlogGenerator from '@/components/blog/BlogGenerator';
import BlogManager from '@/components/blog/BlogManager';
import SeriesManager from '@/components/series/SeriesManager';
import AuthorManager from '@/components/author/AuthorManager';
import ConfigStatus from '@/components/ConfigStatus';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function Home() {
  const [generatedBlog, setGeneratedBlog] = useState<any>(null);

  const handleBlogGenerated = (data: any) => {
    setGeneratedBlog(data);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-slate-900 dark:text-slate-100 mb-4">
            AI博文自动生成系统
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-400">
            智能生成SEO优化的博文内容，支持系列管理和多语言
          </p>
        </div>

        <Tabs defaultValue="generate" className="max-w-6xl mx-auto">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="generate">生成博文</TabsTrigger>
            <TabsTrigger value="manage">文章管理</TabsTrigger>
            <TabsTrigger value="series">系列管理</TabsTrigger>
            <TabsTrigger value="authors">作者管理</TabsTrigger>
            <TabsTrigger value="config">系统配置</TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="mt-8">
            <BlogGenerator onGenerated={handleBlogGenerated} />
          </TabsContent>

          <TabsContent value="manage" className="mt-8">
            <BlogManager />
          </TabsContent>

          <TabsContent value="series" className="mt-8">
            <SeriesManager authorId="00000000-0000-0000-0000-000000000001" />
          </TabsContent>

          <TabsContent value="authors" className="mt-8">
            <AuthorManager />
          </TabsContent>

          <TabsContent value="config" className="mt-8">
            <ConfigStatus />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
