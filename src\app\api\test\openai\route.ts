import { NextResponse } from 'next/server';
import { qwen } from '@/lib/ai/qwen';

export async function GET() {
  try {
    // 测试 Qwen API 连接
    const response = await qwen.chat.completions.create({
      model: 'qwen-plus-latest',
      messages: [
        {
          role: 'user',
          content: '请简单回复"测试成功"'
        }
      ],
      max_tokens: 10,
      temperature: 0
    });

    const content = response.choices[0]?.message?.content;

    return NextResponse.json({
      success: true,
      message: 'Qwen API connection successful',
      response: content,
      model: response.model,
      usage: response.usage
    });

  } catch (error) {
    console.error('Qwen API test error:', error);
    return NextResponse.json(
      {
        error: 'Qwen API test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
