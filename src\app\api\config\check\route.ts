import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // 检查 Qwen API 密钥是否配置
    const qwenApiKey = process.env.QWEN_API_KEY;
    const qwenConfigured = !!(qwenApiKey && qwenApiKey.startsWith('sk-'));
    
    // 检查 Supabase 配置
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    const supabaseConfigured = !!(
      supabaseUrl && 
      supabaseAnonKey && 
      supabaseServiceKey &&
      supabaseUrl !== 'your_supabase_url_here' &&
      supabaseAnonKey !== 'your_supabase_anon_key_here' &&
      supabaseServiceKey !== 'your_supabase_service_role_key_here'
    );

    return NextResponse.json({
      success: true,
      qwen_configured: qwenConfigured,
      supabase_configured: supabaseConfigured,
      details: {
        qwen: qwenConfigured ? 'Qwen API key is configured' : 'Qwen API key is missing or invalid',
        supabase: supabaseConfigured ? 'Supabase is configured' : 'Supabase configuration is incomplete'
      }
    });
    
  } catch (error) {
    console.error('Config check error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
